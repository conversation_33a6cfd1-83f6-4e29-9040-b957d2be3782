# AI-Assisted Email Response System - RAG Service
# This service handles embedding generation and retrieval-augmented generation for email responses

from fastapi import <PERSON>AP<PERSON>, HTTPException
from fastapi.middleware.cors import CORSMiddleware
from pydantic import BaseModel
from typing import List, Optional, Dict, Any
import uvicorn
import logging
from sentence_transformers import SentenceTransformer
import numpy as np
import os
from dotenv import load_dotenv
import asyncio
from contextlib import asynccontextmanager
from rag_pipeline import RAGPipeline
import httpx

# Load environment variables
load_dotenv()

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Global variables
model = None
rag_pipeline = None

@asynccontextmanager
async def lifespan(app: FastAPI):
    # Startup: Load the embedding model and RAG pipeline
    global model, rag_pipeline
    logger.info("Loading embedding model...")
    try:
        model_name = os.getenv("EMBEDDING_MODEL", "intfloat/e5-large-v2")
        model = SentenceTransformer(model_name)
        logger.info(f"✓ Embedding model '{model_name}' loaded successfully")

        # Initialize RAG Pipeline
        ollama_url = os.getenv("OLLAMA_URL", "http://localhost:11434")
        ollama_model = os.getenv("OLLAMA_MODEL", "mistral:7b")
        ingestion_service_url = os.getenv("INGESTION_SERVICE_URL", "http://localhost:8080")

        rag_pipeline = RAGPipeline(
            ollama_base_url=ollama_url,
            ollama_model=ollama_model,
            ingestion_service_url=ingestion_service_url,
            embedding_model_name=model_name,
            max_context_emails=20,
            similarity_threshold=0.5
        )
        logger.info("✓ RAG Pipeline initialized successfully")

    except Exception as e:
        logger.error(f"Failed to load models: {e}")
        raise

    yield

    # Shutdown: Clean up resources
    logger.info("Shutting down RAG service...")
    if rag_pipeline:
        await rag_pipeline.close()

app = FastAPI(
    title="Email AI Assistant - RAG Service",
    version="1.0.0",
    description="Embedding generation and retrieval service for email AI assistant",
    lifespan=lifespan
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:5173", "http://127.0.0.1:5173", "tauri://localhost"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Pydantic models
class EmbeddingRequest(BaseModel):
    text: str
    normalize: Optional[bool] = True

class EmbeddingResponse(BaseModel):
    embedding: List[float]
    model_name: str
    dimension: int

class BatchEmbeddingRequest(BaseModel):
    texts: List[str]
    normalize: Optional[bool] = True

class BatchEmbeddingResponse(BaseModel):
    embeddings: List[List[float]]
    model_name: str
    dimension: int
    count: int

class EmailQuery(BaseModel):
    query: str
    max_results: Optional[int] = 5

class EmailResponse(BaseModel):
    id: str
    subject: str
    content: str
    similarity_score: float

class DraftRequest(BaseModel):
    subject: str
    sender: str
    content: str
    response_context: Optional[str] = "general"  # "lawyer-to-insurance", "insurance-to-lawyer", "general"

class DraftResponse(BaseModel):
    draft: str
    context_emails_count: int
    similar_emails: List[Dict[str, Any]]
    metadata: Dict[str, Any]

class InformationRequest(BaseModel):
    query: str
    context: Optional[str] = None  # Optional email context for information retrieval
    response_context: Optional[str] = "general"  # "lawyer-to-insurance", "insurance-to-lawyer", "general"

class InformationResponse(BaseModel):
    information: str
    context_emails_count: int
    similar_emails: List[Dict[str, Any]]
    metadata: Dict[str, Any]

class ModelListResponse(BaseModel):
    models: List[str]
    current_model: Optional[str]

class ModelChangeRequest(BaseModel):
    model: str

class ModelChangeResponse(BaseModel):
    success: bool
    message: str
    previous_model: Optional[str]
    new_model: str

@app.get("/")
async def root():
    return {
        "message": "Email AI Assistant RAG Service is running",
        "model_loaded": model is not None,
        "rag_pipeline_loaded": rag_pipeline is not None,
        "model_name": getattr(model, 'model_name', 'Unknown') if model else None
    }

@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {
        "status": "healthy" if (model is not None and rag_pipeline is not None) else "unhealthy",
        "model_loaded": model is not None,
        "rag_pipeline_loaded": rag_pipeline is not None,
        "service": "rag_service"
    }

@app.post("/embed", response_model=EmbeddingResponse)
async def generate_embedding(request: EmbeddingRequest):
    """Generate embedding for a single text"""
    if model is None:
        raise HTTPException(status_code=503, detail="Embedding model not loaded")

    try:
        # Generate embedding
        embedding = model.encode(request.text, normalize_embeddings=request.normalize)

        # Convert to list for JSON serialization
        embedding_list = embedding.tolist()

        return EmbeddingResponse(
            embedding=embedding_list,
            model_name=getattr(model, 'model_name', 'unknown'),
            dimension=len(embedding_list)
        )
    except Exception as e:
        logger.error(f"Error generating embedding: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to generate embedding: {str(e)}")

@app.post("/embed_batch", response_model=BatchEmbeddingResponse)
async def generate_batch_embeddings(request: BatchEmbeddingRequest):
    """Generate embeddings for multiple texts"""
    if model is None:
        raise HTTPException(status_code=503, detail="Embedding model not loaded")

    if len(request.texts) > 100:  # Limit batch size
        raise HTTPException(status_code=400, detail="Batch size too large (max 100)")

    try:
        # Generate embeddings
        embeddings = model.encode(request.texts, normalize_embeddings=request.normalize)

        # Convert to list for JSON serialization
        embeddings_list = embeddings.tolist()

        return BatchEmbeddingResponse(
            embeddings=embeddings_list,
            model_name=getattr(model, 'model_name', 'unknown'),
            dimension=len(embeddings_list[0]) if embeddings_list else 0,
            count=len(embeddings_list)
        )
    except Exception as e:
        logger.error(f"Error generating batch embeddings: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to generate embeddings: {str(e)}")

@app.post("/generate_draft", response_model=DraftResponse)
async def generate_draft(request: DraftRequest):
    """Generate a draft email response using RAG with comprehensive compliance logging"""
    if rag_pipeline is None:
        raise HTTPException(status_code=503, detail="RAG pipeline not loaded")

    try:
        logger.info(f"Generating draft for email from {request.sender}")

        # Generate draft using RAG pipeline (includes compliance logging)
        result = await rag_pipeline.generate_draft(
            subject=request.subject,
            sender=request.sender,
            content=request.content,
            email_context=request.response_context
        )

        return DraftResponse(**result)

    except Exception as e:
        logger.error(f"Error generating draft: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to generate draft: {str(e)}")

@app.post("/generate_information", response_model=InformationResponse)
async def generate_information(request: InformationRequest):
    """Generate information response using RAG with comprehensive compliance logging"""
    if rag_pipeline is None:
        raise HTTPException(status_code=503, detail="RAG pipeline not loaded")

    try:
        logger.info(f"Generating information for query: {request.query[:100]}...")

        # Generate information using RAG pipeline (includes compliance logging)
        result = await rag_pipeline.generate_information(
            query=request.query,
            context=request.context,
            response_context=request.response_context
        )

        return InformationResponse(**result)

    except Exception as e:
        logger.error(f"Error generating information: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to generate information: {str(e)}")

@app.get("/available_models", response_model=ModelListResponse)
async def get_available_models():
    """Get list of available Ollama models"""
    try:
        ollama_url = os.getenv("OLLAMA_URL", "http://localhost:11434")

        async with httpx.AsyncClient() as client:
            response = await client.get(f"{ollama_url}/api/tags")

            if response.status_code != 200:
                raise HTTPException(status_code=503, detail="Ollama service unavailable")

            data = response.json()
            models = [model["name"] for model in data.get("models", [])]

            # Get current model from RAG pipeline
            current_model = None
            if rag_pipeline:
                model_info = rag_pipeline.get_model_info()
                current_model = model_info.get("model")

            return ModelListResponse(
                models=models,
                current_model=current_model
            )

    except Exception as e:
        logger.error(f"Error fetching available models: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to fetch models: {str(e)}")

@app.post("/set_model", response_model=ModelChangeResponse)
async def set_model(request: ModelChangeRequest):
    """Change the current Ollama model"""
    global rag_pipeline

    if not rag_pipeline:
        raise HTTPException(status_code=503, detail="RAG pipeline not loaded")

    try:
        # Get current model info
        previous_model_info = rag_pipeline.get_model_info()
        previous_model = previous_model_info.get("model")

        # Verify the model exists
        ollama_url = os.getenv("OLLAMA_URL", "http://localhost:11434")
        async with httpx.AsyncClient() as client:
            response = await client.get(f"{ollama_url}/api/tags")

            if response.status_code != 200:
                raise HTTPException(status_code=503, detail="Ollama service unavailable")

            data = response.json()
            available_models = [model["name"] for model in data.get("models", [])]

            if request.model not in available_models:
                raise HTTPException(
                    status_code=400,
                    detail=f"Model '{request.model}' not found. Available models: {available_models}"
                )

        # Create new RAG pipeline with the selected model
        embedding_model_name = os.getenv("EMBEDDING_MODEL", "intfloat/e5-large-v2")
        ingestion_service_url = os.getenv("INGESTION_SERVICE_URL", "http://localhost:8080")

        rag_pipeline = RAGPipeline(
            llm_provider="ollama",
            llm_config={
                "base_url": ollama_url,
                "model": request.model,
                "temperature": 0.7
            },
            ingestion_service_url=ingestion_service_url,
            embedding_model_name=embedding_model_name,
            max_context_emails=20,
            similarity_threshold=0.5
        )

        logger.info(f"Successfully changed model from '{previous_model}' to '{request.model}'")

        return ModelChangeResponse(
            success=True,
            message=f"Model changed to {request.model}",
            previous_model=previous_model,
            new_model=request.model
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error changing model: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to change model: {str(e)}")

if __name__ == "__main__":
    uvicorn.run(app, host="0.0.0.0", port=8003)
