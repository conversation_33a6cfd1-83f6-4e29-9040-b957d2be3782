// Email Threading and Conversation Detection Module
// Implements RFC 5322 compliant email threading with enhanced metadata support

use std::collections::{HashMap, HashSet};
use uuid::Uuid;
use chrono::{DateTime, Utc};
use serde::{Serialize, Deserialize};
use sha2::{Sha256, Digest};

/// Enhanced email structure with threading and weighting metadata
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct ThreadedEmail {
    pub id: Uuid,
    pub subject: Option<String>,
    pub from: Option<String>,
    pub to: Vec<String>,
    pub sent_date: Option<DateTime<Utc>>,
    pub plain_text_body_raw: Option<String>,
    pub html_body_raw: Option<String>,
    pub cleaned_plain_text_body: Option<String>,
    
    // Threading metadata
    pub message_id: Option<String>,
    pub in_reply_to: Vec<String>,
    pub references: Vec<String>,
    pub thread_id: Option<String>,
    pub conversation_id: Option<String>,
    pub thread_position: Option<u32>,
    
    // Email categorization and weighting
    pub email_type: EmailType,
    pub weight: f32,
    pub folder_path: Option<String>,
    
    // Content analysis
    pub content_hash: Option<String>,
    pub normalized_subject: Option<String>,
    
    // Processing metadata
    pub is_duplicate: bool,
    pub duplicate_of: Option<Uuid>,
    pub processing_notes: Vec<String>,

    // Case cataloging
    pub case_id: Option<Uuid>,
    pub case_subject: Option<String>,
    pub case_participants: Vec<String>,
}

/// Email type classification for weighting
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub enum EmailType {
    Received,  // 34% weight
    Sent,      // 66% weight
    Draft,
    Unknown,
}

impl EmailType {
    pub fn default_weight(&self) -> f32 {
        match self {
            EmailType::Received => 0.34,
            EmailType::Sent => 0.66,
            EmailType::Draft => 0.10,
            EmailType::Unknown => 0.50,
        }
    }
}

/// Conversation thread representation
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct ConversationThread {
    pub thread_id: String,
    pub conversation_id: String,
    pub root_message_id: Option<String>,
    pub subject_normalized: String,
    pub participants: HashSet<String>,
    pub email_ids: Vec<Uuid>,
    pub start_date: Option<DateTime<Utc>>,
    pub last_date: Option<DateTime<Utc>>,
    pub message_count: u32,
}

/// Email case for cataloging related emails
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct EmailCase {
    pub case_id: Uuid,
    pub case_subject: String,
    pub participants: HashSet<String>,
    pub thread_ids: HashSet<String>,
    pub created_at: DateTime<Utc>,
    pub last_activity: DateTime<Utc>,
    pub email_count: u32,
    pub keywords: Vec<String>,
}

/// Email threading engine
pub struct EmailThreader {
    pub threads: HashMap<String, ConversationThread>,
    pub message_id_to_thread: HashMap<String, String>,
    pub subject_to_thread: HashMap<String, String>,
    pub cases: HashMap<Uuid, EmailCase>,
    subject_to_case: HashMap<String, Uuid>,
}

impl EmailThreader {
    pub fn new() -> Self {
        Self {
            threads: HashMap::new(),
            message_id_to_thread: HashMap::new(),
            subject_to_thread: HashMap::new(),
            cases: HashMap::new(),
            subject_to_case: HashMap::new(),
        }
    }

    /// Normalize subject line for threading
    pub fn normalize_subject(subject: &str) -> String {
        let mut normalized = subject.trim().to_lowercase();
        
        // Remove common reply/forward prefixes
        let prefixes = ["re:", "fwd:", "fw:", "aw:", "sv:", "vs:", "wg:"];
        loop {
            let original_len = normalized.len();
            for prefix in &prefixes {
                if normalized.starts_with(prefix) {
                    normalized = normalized[prefix.len()..].trim().to_string();
                }
            }
            // Remove bracketed content like [EXTERNAL] or [SPAM]
            if normalized.starts_with('[') {
                if let Some(end) = normalized.find(']') {
                    normalized = normalized[end + 1..].trim().to_string();
                }
            }
            if normalized.len() == original_len {
                break; // No more changes
            }
        }
        
        // Remove extra whitespace
        normalized = normalized.split_whitespace().collect::<Vec<_>>().join(" ");
        normalized
    }

    /// Generate content hash for duplicate detection
    pub fn generate_content_hash(email: &ThreadedEmail) -> String {
        let mut hasher = Sha256::new();
        
        // Hash key identifying fields
        if let Some(from) = &email.from {
            hasher.update(from.as_bytes());
        }
        if let Some(subject) = &email.normalized_subject {
            hasher.update(subject.as_bytes());
        }
        if let Some(body) = &email.cleaned_plain_text_body {
            // Hash first 1000 characters to avoid issues with very long emails
            // Use char_indices to respect Unicode boundaries
            let content = if body.chars().count() > 1000 {
                body.char_indices()
                    .nth(1000)
                    .map(|(i, _)| &body[..i])
                    .unwrap_or(body)
            } else {
                body
            };
            hasher.update(content.as_bytes());
        }
        if let Some(date) = &email.sent_date {
            hasher.update(date.timestamp().to_string().as_bytes());
        }
        
        format!("{:x}", hasher.finalize())
    }

    /// Detect email type based on folder path and headers
    pub fn detect_email_type(folder_path: &str, _from: &Option<String>, _to: &[String]) -> EmailType {
        let folder_lower = folder_path.to_lowercase();
        
        // Check folder-based classification first
        if folder_lower.contains("sent") || folder_lower.contains("outbox") {
            return EmailType::Sent;
        }
        if folder_lower.contains("draft") {
            return EmailType::Draft;
        }
        if folder_lower.contains("inbox") || folder_lower.contains("received") {
            return EmailType::Received;
        }
        
        // TODO: Add domain-based classification if we know user's email domains
        // For now, default to received if we can't determine
        EmailType::Received
    }

    /// Catalog email into a case based on subject and participants
    pub fn catalog_email(&mut self, email: &mut ThreadedEmail) -> Result<(), Box<dyn std::error::Error>> {
        let normalized_subject = email.normalized_subject.clone()
            .unwrap_or_else(|| "unknown".to_string());

        // Extract participants (from + to addresses)
        let mut participants = HashSet::new();
        if let Some(from) = &email.from {
            participants.insert(from.clone());
        }
        for to in &email.to {
            participants.insert(to.clone());
        }

        // Check if we already have a case for this subject pattern
        let case_id = if let Some(existing_case_id) = self.subject_to_case.get(&normalized_subject) {
            // Update existing case
            if let Some(case) = self.cases.get_mut(existing_case_id) {
                case.participants.extend(participants);
                if let Some(thread_id) = &email.thread_id {
                    case.thread_ids.insert(thread_id.clone());
                }
                case.email_count += 1;
                if let Some(date) = email.sent_date {
                    if date > case.last_activity {
                        case.last_activity = date;
                    }
                }
            }
            *existing_case_id
        } else {
            // Create new case
            let case_id = Uuid::new_v4();
            let mut thread_ids = HashSet::new();
            if let Some(thread_id) = &email.thread_id {
                thread_ids.insert(thread_id.clone());
            }

            let case = EmailCase {
                case_id,
                case_subject: normalized_subject.clone(),
                participants,
                thread_ids,
                created_at: email.sent_date.unwrap_or_else(|| Utc::now()),
                last_activity: email.sent_date.unwrap_or_else(|| Utc::now()),
                email_count: 1,
                keywords: self.extract_keywords(&normalized_subject),
            };

            self.cases.insert(case_id, case);
            self.subject_to_case.insert(normalized_subject, case_id);
            case_id
        };

        // Update email with case information
        email.case_id = Some(case_id);
        if let Some(case) = self.cases.get(&case_id) {
            email.case_subject = Some(case.case_subject.clone());
            email.case_participants = case.participants.iter().cloned().collect();
        }

        Ok(())
    }

    /// Extract keywords from subject for case cataloging
    fn extract_keywords(&self, subject: &str) -> Vec<String> {
        let words: Vec<String> = subject
            .split_whitespace()
            .filter(|word| word.len() > 3) // Only words longer than 3 characters
            .filter(|word| !["from", "with", "about", "regarding", "concerning"].contains(&word.to_lowercase().as_str()))
            .map(|word| word.to_lowercase())
            .collect();

        // Take up to 5 most relevant keywords
        words.into_iter().take(5).collect()
    }

    /// Thread an email into conversations
    pub fn thread_email(&mut self, email: &mut ThreadedEmail) -> Result<String, String> {
        // First, try to find thread by Message-ID references
        let mut thread_id = None;
        
        // Check In-Reply-To headers
        for reply_to_id in &email.in_reply_to {
            if let Some(existing_thread) = self.message_id_to_thread.get(reply_to_id) {
                thread_id = Some(existing_thread.clone());
                break;
            }
        }
        
        // Check References headers if no In-Reply-To match
        if thread_id.is_none() {
            for reference_id in &email.references {
                if let Some(existing_thread) = self.message_id_to_thread.get(reference_id) {
                    thread_id = Some(existing_thread.clone());
                    break;
                }
            }
        }
        
        // Fallback to subject-based threading
        if thread_id.is_none() {
            if let Some(normalized_subject) = &email.normalized_subject {
                if let Some(existing_thread) = self.subject_to_thread.get(normalized_subject) {
                    thread_id = Some(existing_thread.clone());
                }
            }
        }
        
        // Create new thread if no existing thread found
        let final_thread_id = thread_id.unwrap_or_else(|| {
            let new_thread_id = Uuid::new_v4().to_string();
            
            // Create new conversation thread
            let conversation = ConversationThread {
                thread_id: new_thread_id.clone(),
                conversation_id: new_thread_id.clone(),
                root_message_id: email.message_id.clone(),
                subject_normalized: email.normalized_subject.clone().unwrap_or_default(),
                participants: HashSet::new(),
                email_ids: Vec::new(),
                start_date: email.sent_date,
                last_date: email.sent_date,
                message_count: 0,
            };
            
            self.threads.insert(new_thread_id.clone(), conversation);
            new_thread_id
        });
        
        // Update email with thread information
        email.thread_id = Some(final_thread_id.clone());
        email.conversation_id = Some(final_thread_id.clone());
        
        // Update thread tracking
        if let Some(message_id) = &email.message_id {
            self.message_id_to_thread.insert(message_id.clone(), final_thread_id.clone());
        }
        if let Some(normalized_subject) = &email.normalized_subject {
            self.subject_to_thread.insert(normalized_subject.clone(), final_thread_id.clone());
        }
        
        // Update conversation thread
        if let Some(thread) = self.threads.get_mut(&final_thread_id) {
            thread.email_ids.push(email.id);
            thread.message_count += 1;
            
            // Update participants
            if let Some(from) = &email.from {
                thread.participants.insert(from.clone());
            }
            for to_addr in &email.to {
                thread.participants.insert(to_addr.clone());
            }
            
            // Update date range
            if let Some(sent_date) = email.sent_date {
                if thread.start_date.is_none() || thread.start_date.unwrap() > sent_date {
                    thread.start_date = Some(sent_date);
                }
                if thread.last_date.is_none() || thread.last_date.unwrap() < sent_date {
                    thread.last_date = Some(sent_date);
                }
            }
            
            // Set thread position
            email.thread_position = Some(thread.message_count);
        }
        
        Ok(final_thread_id)
    }
}

/// Duplicate detection engine
pub struct DuplicateDetector {
    content_hashes: HashMap<String, Uuid>,
    fuzzy_threshold: f32,
}

impl DuplicateDetector {
    pub fn new(fuzzy_threshold: f32) -> Self {
        Self {
            content_hashes: HashMap::new(),
            fuzzy_threshold,
        }
    }

    /// Check if email is a duplicate or contains mostly quoted content
    pub fn check_duplicate(&mut self, email: &mut ThreadedEmail) -> bool {
        let content_hash = EmailThreader::generate_content_hash(email);
        email.content_hash = Some(content_hash.clone());

        // Check for exact duplicate
        if let Some(existing_id) = self.content_hashes.get(&content_hash) {
            email.is_duplicate = true;
            email.duplicate_of = Some(*existing_id);
            return true;
        }

        // Check for content similarity (fuzzy duplicate detection)
        if let Some(body) = &email.cleaned_plain_text_body {
            for (existing_hash, existing_id) in &self.content_hashes {
                if self.is_content_similar(body, existing_hash) {
                    email.is_duplicate = true;
                    email.duplicate_of = Some(*existing_id);
                    return true;
                }
            }
        }

        self.content_hashes.insert(content_hash, email.id);
        false
    }

    /// Check if two email contents are similar (for detecting quoted content duplicates)
    fn is_content_similar(&self, content1: &str, content2_hash: &str) -> bool {
        // For now, implement a simple similarity check
        // In a more sophisticated implementation, you could use:
        // - Jaccard similarity
        // - Cosine similarity
        // - Edit distance (Levenshtein)

        let content1_words: std::collections::HashSet<&str> = content1
            .split_whitespace()
            .filter(|word| word.len() > 3) // Only consider words longer than 3 chars
            .collect();

        // If content is too short, don't consider it similar
        if content1_words.len() < 10 {
            return false;
        }

        // For now, we'll implement this when we have access to the original content
        // This is a placeholder for more sophisticated similarity detection
        false
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[test]
    fn test_normalize_subject() {
        assert_eq!(EmailThreader::normalize_subject("Re: Test Subject"), "test subject");
        assert_eq!(EmailThreader::normalize_subject("FWD: Re: Test"), "test");
        assert_eq!(EmailThreader::normalize_subject("[EXTERNAL] Important Message"), "important message");
        assert_eq!(EmailThreader::normalize_subject("  RE:  FW:  Test  "), "test");
    }

    #[test]
    fn test_email_type_weights() {
        assert_eq!(EmailType::Sent.default_weight(), 0.66);
        assert_eq!(EmailType::Received.default_weight(), 0.34);
    }

    #[test]
    fn test_detect_email_type() {
        assert_eq!(EmailThreader::detect_email_type("INBOX", &None, &[]), EmailType::Received);
        assert_eq!(EmailThreader::detect_email_type("Sent Items", &None, &[]), EmailType::Sent);
        assert_eq!(EmailThreader::detect_email_type("Drafts", &None, &[]), EmailType::Draft);
    }
}
