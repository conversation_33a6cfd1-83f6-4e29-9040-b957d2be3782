// AI-Assisted Email Response System - Ingestion Service Library
// Core functionality for email parsing, cleaning, and database operations

use std::fs;
use std::io;
use uuid::Uuid;
use chrono::{DateTime, Utc};
use serde::{Serialize, Deserialize};
use dotenv::dotenv;
use reqwest::Client;
use qdrant_client::{
    Qdrant,
    qdrant::{
        CreateCollection, Distance, VectorParams,
        PointStruct, SearchPoints, UpsertPoints, ScrollPoints, Value, WithPayloadSelector, WithVectorsSelector
    },
};

// Import email threading functionality
pub use crate::email_threading::{ThreadedEmail, EmailThreader, EmailType, ConversationThread, EmailCase};
use std::collections::HashMap;
use mail_parser::MessageParser;

// New modules for enhanced email processing
pub mod email_threading;
pub mod enhanced_mbox;
pub mod test_data;
pub mod thunderbird_processor;

// Email parsing and cleaning functions

/// Converts HTML content to plain text with proper formatting.
/// Uses html2text crate for robust HTML parsing and conversion.
pub fn html_to_plain_text(html: &str) -> String {
    html2text::from_read(html.as_bytes(), 80)
}

/// Strips email signatures and quoted content from email text.
/// Removes lines starting with '>' (quoted content) and content after signature markers.
pub fn strip_signatures_and_quotes(text: &str) -> String {
    let mut cleaned_lines = Vec::new();
    
    for line in text.lines() {
        // Check for common signature markers
        if line.trim() == "--" || 
           (line.trim().starts_with("-- ") && line.trim().len() < 50) ||
           line.trim().starts_with("---") ||
           line.contains("Sent from my") ||
           line.contains("Best regards") ||
           line.contains("Kind regards") {
            break; // Stop processing once we hit a signature
        }
        
        // Skip quoted content (lines starting with >)
        if line.starts_with('>') || line.starts_with(" >") {
            continue;
        }
        
        // Skip forwarded message headers
        if line.starts_with("-----Original Message-----") ||
           line.starts_with("From:") && cleaned_lines.is_empty() {
            continue;
        }
        
        cleaned_lines.push(line);
    }
    
    // Remove excessive whitespace at the end
    while let Some(last_line) = cleaned_lines.last() {
        if last_line.trim().is_empty() {
            cleaned_lines.pop();
        } else {
            break;
        }
    }
    
    cleaned_lines.join("\n")
}

// Email struct for parsed emails
#[derive(Debug, Clone, PartialEq, Serialize, Deserialize)]
pub struct ParsedEmail {
    pub id: Uuid,
    pub subject: Option<String>,
    pub from: Option<String>,
    pub to: Vec<String>,
    pub sent_date: Option<DateTime<Utc>>,
    pub plain_text_body_raw: Option<String>,
    pub html_body_raw: Option<String>,
    pub cleaned_plain_text_body: Option<String>,
    pub folder_type: Option<String>, // Added to support X-Folder-Type header from mail cleaner
}

// Message struct for Qdrant storage (metadata stored as payload)
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct Message {
    pub id: Uuid,
    pub subject: Option<String>,
    pub from_address: Option<String>,
    pub to_addresses: Vec<String>,
    pub sent_date: Option<DateTime<Utc>>,
    pub plain_text_body_raw: Option<String>,
    pub html_body_raw: Option<String>,
    pub cleaned_plain_text_body: Option<String>,
    pub file_path: Option<String>,
    pub created_at: DateTime<Utc>,
    pub updated_at: DateTime<Utc>,

    // Enhanced threading metadata
    pub thread_id: Option<String>,
    pub conversation_id: Option<String>,
    pub thread_position: Option<u32>,
    pub email_weight: Option<f32>,
    pub email_type: Option<String>,
    pub is_duplicate: Option<bool>,

    // Message headers for threading
    pub message_id: Option<String>,
    pub in_reply_to: Vec<String>,
    pub references: Vec<String>,

    // Content analysis
    pub content_hash: Option<String>,
    pub normalized_subject: Option<String>,

    // Case cataloging metadata
    pub case_id: Option<String>,
    pub case_subject: Option<String>,
    pub case_participants: Vec<String>,

    // Country identification for case organization
    pub country_uuid: Option<String>,
}

// Qdrant search result
#[derive(Debug, Clone, Serialize, Deserialize)]
pub struct SearchResult {
    pub message: Message,
    pub score: f32,
}

impl From<ParsedEmail> for Message {
    fn from(parsed: ParsedEmail) -> Self {
        let now = Utc::now();
        Message {
            id: parsed.id,
            subject: parsed.subject,
            from_address: parsed.from,
            to_addresses: parsed.to,
            sent_date: parsed.sent_date,
            plain_text_body_raw: parsed.plain_text_body_raw,
            html_body_raw: parsed.html_body_raw,
            cleaned_plain_text_body: parsed.cleaned_plain_text_body,
            file_path: None,
            created_at: now,
            updated_at: now,

            // Initialize threading metadata as None for standard emails
            thread_id: None,
            conversation_id: None,
            thread_position: None,
            email_weight: None,
            email_type: None,
            is_duplicate: None,

            // Initialize message headers
            message_id: None,
            in_reply_to: Vec::new(),
            references: Vec::new(),

            // Initialize content analysis
            content_hash: None,
            normalized_subject: None,

            // Initialize case cataloging
            case_id: None,
            case_subject: None,
            case_participants: Vec::new(),

            // Initialize country identification
            country_uuid: None,
        }
    }
}

impl From<ParsedEmail> for ThreadedEmail {
    fn from(parsed: ParsedEmail) -> Self {
        ThreadedEmail {
            id: parsed.id,
            subject: parsed.subject.clone(),
            from: parsed.from.clone(),
            to: parsed.to.clone(),
            sent_date: parsed.sent_date,
            plain_text_body_raw: parsed.plain_text_body_raw.clone(),
            html_body_raw: parsed.html_body_raw.clone(),
            cleaned_plain_text_body: parsed.cleaned_plain_text_body.clone(),

            // Extract threading headers from raw headers if available
            message_id: None, // Will be extracted from headers
            in_reply_to: Vec::new(), // Will be extracted from headers
            references: Vec::new(), // Will be extracted from headers
            thread_id: None,
            conversation_id: None,
            thread_position: None,

            // Default email categorization
            email_type: EmailType::Unknown,
            weight: 1.0,
            folder_path: None,

            // Content analysis
            content_hash: None,
            normalized_subject: None,

            // Processing metadata
            is_duplicate: false,
            duplicate_of: None,
            processing_notes: Vec::new(),

            // Case cataloging
            case_id: None,
            case_subject: None,
            case_participants: Vec::new(),
        }
    }
}

impl From<ThreadedEmail> for Message {
    fn from(threaded: ThreadedEmail) -> Self {
        let now = Utc::now();
        Message {
            id: threaded.id,
            subject: threaded.subject,
            from_address: threaded.from,
            to_addresses: threaded.to,
            sent_date: threaded.sent_date,
            plain_text_body_raw: threaded.plain_text_body_raw,
            html_body_raw: threaded.html_body_raw,
            cleaned_plain_text_body: threaded.cleaned_plain_text_body,
            file_path: threaded.folder_path,
            created_at: now,
            updated_at: now,

            // Threading metadata from ThreadedEmail
            thread_id: threaded.thread_id,
            conversation_id: threaded.conversation_id,
            thread_position: threaded.thread_position,
            email_weight: Some(threaded.weight),
            email_type: Some(format!("{:?}", threaded.email_type).to_lowercase()),
            is_duplicate: Some(threaded.is_duplicate),

            // Message headers
            message_id: threaded.message_id,
            in_reply_to: threaded.in_reply_to,
            references: threaded.references,

            // Content analysis
            content_hash: threaded.content_hash,
            normalized_subject: threaded.normalized_subject,

            // Case cataloging
            case_id: threaded.case_id.map(|uuid| uuid.to_string()),
            case_subject: threaded.case_subject,
            case_participants: threaded.case_participants,

            // Country identification
            country_uuid: {
                // Extract country UUID from processing notes
                threaded.processing_notes.iter()
                    .find(|note| note.starts_with("Country identified:"))
                    .and_then(|note| {
                        // Extract UUID from note like "Country identified: US (UUID: abc-def-...)"
                        if let Some(start) = note.find("UUID: ") {
                            let uuid_part = &note[start + 6..];
                            if let Some(end) = uuid_part.find(')') {
                                Some(uuid_part[..end].to_string())
                            } else {
                                Some(uuid_part.to_string())
                            }
                        } else {
                            None
                        }
                    })
            },
        }
    }
}

/// Parses a single .eml file into a ParsedEmail struct.
/// Applies cleaning functions and handles errors.
pub fn parse_eml(file_path: &str) -> Result<ParsedEmail, io::Error> {
    let content = fs::read_to_string(file_path)?;
    parse_email_from_string(&content)
}

/// Parses an mbox file or .txt file and returns a vector of ParsedEmail structs.
/// For large files, uses true streaming to avoid memory issues.
/// For two-pass processing, reads from the processed streaming output.
pub fn parse_mbox(file_path: &str) -> Result<Vec<ParsedEmail>, io::Error> {
    // Check if this is a .txt file and handle it differently
    if file_path.to_lowercase().ends_with(".txt") {
        return parse_txt_file(file_path);
    }

    // Check file size first
    let file_metadata = fs::metadata(file_path)?;
    let file_size = file_metadata.len();
    let file_size_mb = file_size as f64 / (1024.0 * 1024.0);

    println!("Processing mbox file: {} ({:.1} MB)", file_path, file_size_mb);

    // Now that S/MIME signatures are handled safely, we can use in-memory parsing
    // for better threading and duplicate detection
    if file_size > 1024 * 1024 * 1024 {  // Only use streaming for files > 1GB
        println!("Very large file detected - using streaming approach");

        // First pass: Stream process to avoid memory issues
        parse_mbox_streaming_old(file_path)?;

        // Second pass: Read the processed output for threading/deduplication
        let processed_path = if file_path.ends_with(".mbox") {
            file_path.replace(".mbox", "_processed.mbox")
        } else {
            format!("{}_processed.mbox", file_path)
        };

        println!("Reading processed output for threading: {}", processed_path);
        parse_mbox_in_memory(&processed_path)
    } else {
        println!("Using in-memory parser with S/MIME signature filtering");
        parse_mbox_in_memory(file_path)
    }
}

/// Parses an mbox file or .txt file in batches, calling a callback for each batch to avoid memory accumulation
pub fn parse_mbox_in_batches<F>(file_path: &str, batch_size: usize, mut callback: F) -> Result<u32, io::Error>
where
    F: FnMut(Vec<ParsedEmail>) -> Result<(), Box<dyn std::error::Error>>,
{
    // Check if this is a .txt file and handle it differently
    if file_path.to_lowercase().ends_with(".txt") {
        return parse_txt_file_in_batches(file_path, batch_size, callback);
    }

    use std::io::{BufReader, Read};

    let file_metadata = fs::metadata(file_path)?;
    let file_size = file_metadata.len();
    let file_size_mb = file_size as f64 / (1024.0 * 1024.0);

    println!("Processing mbox file in batches: {} ({:.1} MB)", file_path, file_size_mb);

    let file = fs::File::open(file_path)?;
    let mut reader = BufReader::new(file);
    let mut current_batch = Vec::new();
    let mut current_email_bytes = Vec::new();
    let mut in_email = false;
    let mut total_emails_processed = 0u32;
    let mut parsing_errors = 0;

    // Read file in chunks to avoid loading everything into memory
    let mut buffer = [0; 8192]; // 8KB chunks
    let mut leftover_bytes = Vec::new();

    loop {
        let bytes_read = reader.read(&mut buffer)?;
        if bytes_read == 0 {
            break; // End of file
        }

        // Combine leftover bytes from previous chunk with new data
        let mut chunk_data = Vec::new();
        chunk_data.extend_from_slice(&leftover_bytes);
        chunk_data.extend_from_slice(&buffer[..bytes_read]);
        leftover_bytes.clear();

        // Process lines in this chunk
        let mut line_start = 0;
        for i in 0..chunk_data.len() {
            if chunk_data[i] == b'\n' || i == chunk_data.len() - 1 {
                let line_end = if i == chunk_data.len() - 1 && chunk_data[i] != b'\n' {
                    i + 1
                } else {
                    i
                };

                let line_bytes = &chunk_data[line_start..line_end];

                // If this is the last line in the chunk and doesn't end with newline,
                // save it for the next chunk (unless it's the end of file)
                if i == chunk_data.len() - 1 && chunk_data[i] != b'\n' && bytes_read == buffer.len() {
                    leftover_bytes.extend_from_slice(line_bytes);
                    break;
                }

                // Convert line to string for pattern matching (safe for ASCII patterns)
                let line_str = String::from_utf8_lossy(line_bytes);
                let line_str = line_str.trim_end_matches('\r'); // Handle CRLF

                // Check for mbox separator lines
                if (line_str.starts_with("From ") && line_str.contains("@")) ||
                   line_str.starts_with("From - ") {
                    // Process the previous email if we have one
                    if in_email && !current_email_bytes.is_empty() {
                        match parse_email_from_bytes_safe(&current_email_bytes) {
                            Ok(parsed_email) => {
                                current_batch.push(parsed_email);
                                total_emails_processed += 1;

                                // Process batch when it reaches the desired size
                                if current_batch.len() >= batch_size {
                                    if let Err(e) = callback(current_batch) {
                                        eprintln!("Error processing batch: {}", e);
                                        return Err(io::Error::new(io::ErrorKind::Other, e.to_string()));
                                    }
                                    current_batch = Vec::new();

                                    // Progress indicator
                                    if total_emails_processed % 1000 == 0 {
                                        println!("  Processed {} emails...", total_emails_processed);
                                    }
                                }
                            }
                            Err(e) => {
                                parsing_errors += 1;
                                eprintln!("Warning: Failed to parse email #{} in mbox: {}", total_emails_processed + 1, e);
                            }
                        }
                    }
                    // Start a new email (skip the "From " separator line)
                    current_email_bytes.clear();
                    in_email = true;
                } else if in_email {
                    // Add the line bytes to current email, preserving original encoding
                    current_email_bytes.extend_from_slice(line_bytes);
                    if chunk_data[i] == b'\n' {
                        current_email_bytes.push(b'\n');
                    }
                }

                line_start = i + 1;
            }
        }
    }

    // Process any remaining leftover bytes
    if !leftover_bytes.is_empty() && in_email {
        current_email_bytes.extend_from_slice(&leftover_bytes);
    }

    // Process the last email
    if in_email && !current_email_bytes.is_empty() {
        match parse_email_from_bytes_safe(&current_email_bytes) {
            Ok(parsed_email) => {
                current_batch.push(parsed_email);
                total_emails_processed += 1;
            }
            Err(e) => {
                parsing_errors += 1;
                eprintln!("Warning: Failed to parse last email in mbox: {}", e);
            }
        }
    }

    // Process any remaining emails in the final batch
    if !current_batch.is_empty() {
        if let Err(e) = callback(current_batch) {
            eprintln!("Error processing final batch: {}", e);
            return Err(io::Error::new(io::ErrorKind::Other, e.to_string()));
        }
    }

    // Report parsing statistics
    if parsing_errors > 0 {
        println!("Parsing summary: {} emails processed, {} failed",
                 total_emails_processed, parsing_errors);
    } else {
        println!("Successfully processed {} emails from mbox file", total_emails_processed);
    }

    Ok(total_emails_processed)
}

/// Streaming mbox parser that handles large files without loading everything into memory
/// Uses binary reading to handle mixed encodings safely
/// FIXED: Now processes emails in small batches to avoid memory accumulation
fn parse_mbox_streaming_old(file_path: &str) -> Result<Vec<ParsedEmail>, io::Error> {
    use std::io::{BufReader, Read, Write, BufRead};
    use std::fs::OpenOptions;

    let file = fs::File::open(file_path)?;
    let mut reader = BufReader::new(file);

    // 🎯 TRUE STREAMING: No email accumulation, process immediately
    let mut current_email_bytes = Vec::new();
    let mut in_email = false;
    let mut total_emails_found = 0;
    let mut parsing_errors = 0;
    let mut successfully_processed = 0;

    // 🎯 TARGETED MONITORING: Track specific problematic emails
    let problematic_email_numbers = vec![3963, 3964, 3985, 3986];

    // Create output file for processed emails (instead of accumulating in memory)
    // 🚨 CRITICAL FIX: Ensure we never overwrite the input file
    let output_path = if file_path.ends_with(".mbox") {
        file_path.replace(".mbox", "_processed.mbox")
    } else {
        format!("{}_processed.mbox", file_path)
    };

    // Double-check we're not overwriting the input file
    if output_path == file_path {
        return Err(io::Error::new(io::ErrorKind::InvalidInput,
            "Output path would overwrite input file - this is prevented to avoid data loss"));
    }

    let mut output_file = OpenOptions::new()
        .create(true)
        .write(true)
        .truncate(true)
        .open(&output_path)?;

    println!("Processing emails to: {}", output_path);

    // Read file in chunks to avoid loading everything into memory
    let mut buffer = [0; 8192]; // 8KB chunks
    let mut leftover_bytes = Vec::new();

    loop {
        let bytes_read = reader.read(&mut buffer)?;
        if bytes_read == 0 {
            break; // End of file
        }

        // Combine leftover bytes from previous chunk with new data
        let mut chunk_data = Vec::new();
        chunk_data.extend_from_slice(&leftover_bytes);
        chunk_data.extend_from_slice(&buffer[..bytes_read]);
        leftover_bytes.clear();

        // Process lines in this chunk
        let mut line_start = 0;
        for i in 0..chunk_data.len() {
            if chunk_data[i] == b'\n' || i == chunk_data.len() - 1 {
                let line_end = if i == chunk_data.len() - 1 && chunk_data[i] != b'\n' {
                    i + 1
                } else {
                    i
                };

                let line_bytes = &chunk_data[line_start..line_end];

                // If this is the last line in the chunk and doesn't end with newline,
                // save it for the next chunk (unless it's the end of file)
                if i == chunk_data.len() - 1 && chunk_data[i] != b'\n' && bytes_read == buffer.len() {
                    leftover_bytes.extend_from_slice(line_bytes);
                    break;
                }

                // Convert line to string for pattern matching (safe for ASCII patterns)
                let line_str = String::from_utf8_lossy(line_bytes);
                let line_str = line_str.trim_end_matches('\r'); // Handle CRLF

                // Check for mbox separator lines
                if (line_str.starts_with("From ") && line_str.contains("@")) ||
                   line_str.starts_with("From - ") {
                    // Process the previous email if we have one
                    if in_email && !current_email_bytes.is_empty() {
                        total_emails_found += 1;

                        // 🎯 MEMORY SPIKE DETECTION: Check memory before processing each email
                        #[cfg(target_os = "windows")]
                        let memory_before_email = get_current_memory_usage_mb();
                        #[cfg(not(target_os = "windows"))]
                        let memory_before_email = 0.0;

                        // Check email size before parsing and log detailed info
                        let email_size_mb = current_email_bytes.len() as f64 / (1024.0 * 1024.0);



                        // 🚨 DYNAMIC DETECTION: Skip emails that are likely to cause memory issues
                        let should_skip_email = if email_size_mb > 50.0 {
                            true // Skip very large emails
                        } else if email_size_mb > 10.0 {
                            // For moderately large emails, check content for problematic patterns
                            let preview = String::from_utf8_lossy(&current_email_bytes[..std::cmp::min(1000, current_email_bytes.len())]);

                            // Skip emails with multiple problematic indicators
                            let has_base64 = preview.contains("Content-Transfer-Encoding: base64");
                            let has_application = preview.contains("Content-Type: application/");
                            let has_attachment = preview.contains("attachment");
                            let has_multipart = preview.contains("multipart/");

                            // Skip if it has multiple risk factors
                            (has_base64 && has_application) ||
                            (has_base64 && has_attachment && email_size_mb > 20.0) ||
                            (has_application && has_multipart && email_size_mb > 15.0)
                        } else {
                            false
                        };

                        if should_skip_email {
                            parsing_errors += 1;
                            eprintln!("🚫 SKIPPING email #{} - too large ({:.1} MB)", total_emails_found, email_size_mb);
                        } else {
                            // 🎯 TARGETED MONITORING: Special handling for problematic emails
                            let is_problematic_email = problematic_email_numbers.contains(&total_emails_found);

                            // Monitor memory for problematic emails only
                            let memory_before = if is_problematic_email {
                                #[cfg(target_os = "windows")]
                                {
                                    Some(get_current_memory_usage_mb())
                                }
                                #[cfg(not(target_os = "windows"))]
                                {
                                    None
                                }
                            } else {
                                None
                            };

                            // 🚀 PROCESS EMAIL: Even problematic ones, but with monitoring
                            // 🚀 TRUE STREAMING: Process email immediately without accumulation
                            match parse_email_from_bytes_safe(&current_email_bytes) {
                                Ok(parsed_email) => {


                                    // Write TEXT-ONLY email to output file (not the original binary content)
                                    write_text_only_email_to_file(&mut output_file, &parsed_email)?;

                                    successfully_processed += 1;


                                },
                                Err(e) => {
                                    parsing_errors += 1;
                                    eprintln!("Warning: Failed to parse email #{} in mbox: {}", total_emails_found, e);
                                }
                            }
                        }

                        // Progress monitoring - every 300 emails
                        if total_emails_found % 300 == 0 {
                            println!("  Processed {} emails", total_emails_found);
                        }
                    }
                    // Start a new email (skip the "From " separator line)
                    current_email_bytes.clear();
                    in_email = true;
                } else if in_email {
                    // Add the line bytes to current email, preserving original encoding
                    current_email_bytes.extend_from_slice(line_bytes);
                    if chunk_data[i] == b'\n' {
                        current_email_bytes.push(b'\n');
                    }
                }

                line_start = i + 1;
            }
        }
    }

    // Process any remaining leftover bytes
    if !leftover_bytes.is_empty() && in_email {
        current_email_bytes.extend_from_slice(&leftover_bytes);
    }

    // Process the last email
    if in_email && !current_email_bytes.is_empty() {
        total_emails_found += 1;
        let email_size_mb = current_email_bytes.len() as f64 / (1024.0 * 1024.0);

        let is_problematic_email = problematic_email_numbers.contains(&total_emails_found);

        if email_size_mb > 50.0 {
            parsing_errors += 1;
            eprintln!("Warning: Skipping last email #{} - too large ({:.1} MB)", total_emails_found, email_size_mb);
        } else {
            match parse_email_from_bytes_safe(&current_email_bytes) {
                Ok(parsed_email) => {
                    // Write TEXT-ONLY email to output file (not the original binary content)
                    write_text_only_email_to_file(&mut output_file, &parsed_email)?;
                    successfully_processed += 1;
                },
                Err(e) => {
                    parsing_errors += 1;
                    eprintln!("Warning: Failed to parse last email #{} in mbox: {}", total_emails_found, e);
                }
            }
        }
    }

    // Report processing statistics
    println!("Streaming complete: {} emails processed, {} failed", successfully_processed, parsing_errors);
    println!("Output written to: {}", output_path);

    // Return empty vector since we're doing true streaming (emails written to file)
    Ok(Vec::new())
}

/// Original in-memory mbox parser (kept for small files)
fn parse_mbox_in_memory(file_path: &str) -> Result<Vec<ParsedEmail>, io::Error> {
    // Read file as bytes to preserve encoding
    let bytes = fs::read(file_path)?;
    let mut emails = Vec::new();
    let mut current_email_bytes = Vec::new();
    let mut in_email = false;
    let mut line_start = 0;
    let mut total_emails_found = 0;
    let mut parsing_errors = 0;

    // Process the mbox file line by line in bytes to preserve encoding
    for i in 0..bytes.len() {
        if bytes[i] == b'\n' || i == bytes.len() - 1 {
            let line_end = if i == bytes.len() - 1 { i + 1 } else { i };
            let line_bytes = &bytes[line_start..line_end];

            // Convert line to string for pattern matching (this is safe for ASCII patterns)
            let line_str = String::from_utf8_lossy(line_bytes);
            let line_str = line_str.trim_end_matches('\r'); // Handle CRLF

            // Check for mbox separator lines
            if (line_str.starts_with("From ") && line_str.contains("@")) ||
               (line_str.starts_with("From - ")) {
                // Process the previous email if we have one
                if in_email && !current_email_bytes.is_empty() {
                    total_emails_found += 1;

                    // Check email size before parsing (50MB limit)
                    let email_size_mb = current_email_bytes.len() as f64 / (1024.0 * 1024.0);
                    if email_size_mb > 50.0 {
                        parsing_errors += 1;
                        eprintln!("Warning: Skipping email #{} - too large ({:.1} MB)", total_emails_found, email_size_mb);
                    } else {
                        match parse_email_from_bytes_safe(&current_email_bytes) {
                            Ok(parsed_email) => emails.push(parsed_email),
                            Err(e) => {
                                parsing_errors += 1;
                                eprintln!("Warning: Failed to parse email #{} in mbox: {}", total_emails_found, e);
                                // Continue processing other emails
                            }
                        }
                    }
                }
                // Start a new email (skip the "From " separator line)
                current_email_bytes.clear();
                in_email = true;
            } else if in_email {
                // Add the line bytes to current email, preserving original encoding
                current_email_bytes.extend_from_slice(line_bytes);
                if i < bytes.len() - 1 {
                    current_email_bytes.push(b'\n');
                }
            }

            line_start = i + 1;
        }
    }

    // Process the last email
    if in_email && !current_email_bytes.is_empty() {
        total_emails_found += 1;

        // Check email size before parsing (50MB limit)
        let email_size_mb = current_email_bytes.len() as f64 / (1024.0 * 1024.0);
        if email_size_mb > 50.0 {
            parsing_errors += 1;
            eprintln!("Warning: Skipping last email #{} - too large ({:.1} MB)", total_emails_found, email_size_mb);
        } else {
            match parse_email_from_bytes_safe(&current_email_bytes) {
                Ok(parsed_email) => emails.push(parsed_email),
                Err(e) => {
                    parsing_errors += 1;
                    eprintln!("Warning: Failed to parse last email #{} in mbox: {}", total_emails_found, e);
                }
            }
        }
    }

    // Report parsing statistics
    if parsing_errors > 0 {
        println!("Parsing summary: {} emails found, {} successfully parsed, {} failed",
                 total_emails_found, emails.len(), parsing_errors);
    } else {
        println!("Successfully parsed {} emails from mbox file", emails.len());
    }

    Ok(emails)
}

/// Streaming mbox parser that processes emails one by one without loading entire file into memory
pub fn parse_mbox_streaming<F>(file_path: &str, mut callback: F) -> Result<(), io::Error>
where
    F: FnMut(ParsedEmail),
{
    use std::fs::File;
    use std::io::{BufReader, BufRead};

    let file = File::open(file_path)?;
    let mut reader = BufReader::new(file);
    let mut current_email = String::new();
    let mut line = String::new();
    let mut email_count = 0;
    let mut in_email = false;

    loop {
        line.clear();
        let bytes_read = reader.read_line(&mut line)?;
        if bytes_read == 0 {
            break; // EOF
        }

        // Check for email separator
        if line.starts_with("From ") && in_email {
            // Process the current email
            if !current_email.is_empty() {
                if let Ok(parsed_email) = parse_email_from_string_streaming(&current_email) {
                    callback(parsed_email);
                    email_count += 1;

                    if email_count % 1000 == 0 {
                        println!("  Streamed {} emails...", email_count);
                    }
                }
            }
            current_email.clear();
        }

        current_email.push_str(&line);
        in_email = true;
    }

    // Process the last email
    if !current_email.is_empty() {
        if let Ok(parsed_email) = parse_email_from_string_streaming(&current_email) {
            callback(parsed_email);
            email_count += 1;
        }
    }

    println!("Streaming complete: {} emails processed", email_count);
    Ok(())
}

/// Parse a single email from string content for streaming
fn parse_email_from_string_streaming(content: &str) -> Result<ParsedEmail, io::Error> {
    let lines: Vec<&str> = content.lines().collect();
    if lines.is_empty() {
        return Err(io::Error::new(io::ErrorKind::InvalidData, "Empty email content"));
    }

    let mut subject = String::new();
    let mut from = String::new();
    let mut to_string = String::new();
    let mut sent_date = String::new();
    let mut in_headers = true;
    let mut body_lines = Vec::new();

    for line in lines {
        if in_headers {
            if line.is_empty() {
                in_headers = false;
                continue;
            }

            if line.starts_with("Subject: ") {
                subject = line[9..].trim().to_string();
            } else if line.starts_with("From: ") {
                from = line[6..].trim().to_string();
            } else if line.starts_with("To: ") {
                to_string = line[4..].trim().to_string();
            } else if line.starts_with("Date: ") {
                sent_date = line[6..].trim().to_string();
            }
        } else {
            body_lines.push(line);
        }
    }

    // Parse to addresses
    let to_addresses: Vec<String> = if to_string.is_empty() {
        Vec::new()
    } else {
        to_string.split(',').map(|s| s.trim().to_string()).collect()
    };

    // Parse date
    let parsed_date = if sent_date.is_empty() {
        None
    } else {
        chrono::DateTime::parse_from_rfc2822(&sent_date)
            .map(|dt| dt.with_timezone(&chrono::Utc))
            .ok()
    };

    Ok(ParsedEmail {
        id: uuid::Uuid::new_v4(),
        subject: if subject.is_empty() { None } else { Some(subject) },
        from: if from.is_empty() { None } else { Some(from) },
        to: to_addresses,
        sent_date: parsed_date,
        plain_text_body_raw: Some(content.to_string()),
        html_body_raw: None,
        cleaned_plain_text_body: Some(body_lines.join("\n")),
        folder_type: Some("Inbox".to_string()),
    })
}

/// Parses a Thunderbird .txt export file and returns a vector of ParsedEmail structs
pub fn parse_txt_file(file_path: &str) -> Result<Vec<ParsedEmail>, io::Error> {
    use std::fs::File;
    use std::io::{BufReader, BufRead};

    let file_metadata = fs::metadata(file_path)?;
    let file_size = file_metadata.len();
    let file_size_mb = file_size as f64 / (1024.0 * 1024.0);

    println!("Processing Thunderbird .txt file: {} ({:.1} MB)", file_path, file_size_mb);

    let file = File::open(file_path)?;
    let reader = BufReader::new(file);

    let mut emails = Vec::new();
    let mut current_email_lines = Vec::new();
    let mut parsing_errors = 0;
    let mut total_emails_found = 0;

    for line_result in reader.lines() {
        let line = line_result?;

        // Check if this line starts a new email (Subject: at the beginning of line with no indentation)
        if line.starts_with("Subject:") && !current_email_lines.is_empty() {
            // Process the previous email
            total_emails_found += 1;
            match parse_txt_email_from_lines(&current_email_lines, total_emails_found) {
                Ok(parsed_email) => emails.push(parsed_email),
                Err(e) => {
                    parsing_errors += 1;
                    eprintln!("Warning: Failed to parse email #{} in txt file: {}", total_emails_found, e);
                }
            }
            current_email_lines.clear();
        }

        current_email_lines.push(line);
    }

    // Process the last email if any
    if !current_email_lines.is_empty() {
        total_emails_found += 1;
        match parse_txt_email_from_lines(&current_email_lines, total_emails_found) {
            Ok(parsed_email) => emails.push(parsed_email),
            Err(e) => {
                parsing_errors += 1;
                eprintln!("Warning: Failed to parse last email #{} in txt file: {}", total_emails_found, e);
            }
        }
    }

    // Report parsing statistics
    if parsing_errors > 0 {
        println!("Parsing summary: {} emails found, {} successfully parsed, {} failed",
                 total_emails_found, emails.len(), parsing_errors);
    } else {
        println!("Successfully parsed {} emails from txt file", emails.len());
    }

    Ok(emails)
}

/// Parses a Thunderbird .txt export file in batches, calling a callback for each batch
pub fn parse_txt_file_in_batches<F>(file_path: &str, batch_size: usize, mut callback: F) -> Result<u32, io::Error>
where
    F: FnMut(Vec<ParsedEmail>) -> Result<(), Box<dyn std::error::Error>>,
{
    use std::fs::File;
    use std::io::{BufReader, BufRead};

    let file_metadata = fs::metadata(file_path)?;
    let file_size = file_metadata.len();
    let file_size_mb = file_size as f64 / (1024.0 * 1024.0);

    println!("Processing Thunderbird .txt file in batches: {} ({:.1} MB)", file_path, file_size_mb);

    let file = File::open(file_path)?;
    let reader = BufReader::new(file);

    let mut current_batch = Vec::new();
    let mut current_email_lines = Vec::new();
    let mut parsing_errors = 0;
    let mut total_emails_processed = 0;

    for line_result in reader.lines() {
        let line = line_result?;

        // Check if this line starts a new email (Subject: at the beginning of line with no indentation)
        if line.starts_with("Subject:") && !current_email_lines.is_empty() {
            // Process the previous email
            match parse_txt_email_from_lines(&current_email_lines, total_emails_processed + 1) {
                Ok(parsed_email) => {
                    current_batch.push(parsed_email);
                    total_emails_processed += 1;

                    // If batch is full, process it
                    if current_batch.len() >= batch_size {
                        if let Err(e) = callback(current_batch) {
                            eprintln!("Error processing batch: {}", e);
                        }
                        current_batch = Vec::new();
                    }
                },
                Err(e) => {
                    parsing_errors += 1;
                    eprintln!("Warning: Failed to parse email #{} in txt file: {}", total_emails_processed + 1, e);
                }
            }
            current_email_lines.clear();
        }

        current_email_lines.push(line);
    }

    // Process the last email if any
    if !current_email_lines.is_empty() {
        match parse_txt_email_from_lines(&current_email_lines, total_emails_processed + 1) {
            Ok(parsed_email) => {
                current_batch.push(parsed_email);
                total_emails_processed += 1;
            },
            Err(e) => {
                parsing_errors += 1;
                eprintln!("Warning: Failed to parse last email in txt file: {}", e);
            }
        }
    }

    // Process any remaining emails in the final batch
    if !current_batch.is_empty() {
        if let Err(e) = callback(current_batch) {
            eprintln!("Error processing final batch: {}", e);
        }
    }

    // Report parsing statistics
    if parsing_errors > 0 {
        println!("Batch processing summary: {} emails processed, {} failed",
                 total_emails_processed, parsing_errors);
    } else {
        println!("Successfully processed {} emails from txt file in batches", total_emails_processed);
    }

    Ok(total_emails_processed)
}

/// Parse a single email from .txt file lines
fn parse_txt_email_from_lines(lines: &[String], email_number: u32) -> Result<ParsedEmail, io::Error> {
    use uuid::Uuid;
    use chrono::{DateTime, Utc};

    let mut subject = None;
    let mut from = None;
    let mut to = Vec::new();
    let mut sent_date = None;
    let mut body_lines = Vec::new();
    let mut in_headers = true;

    for line in lines {
        let line = line.trim();

        if in_headers {
            if line.is_empty() {
                in_headers = false;
                continue;
            }

            // Parse headers
            if line.to_lowercase().starts_with("subject:") {
                subject = Some(line[8..].trim().to_string());
            } else if line.to_lowercase().starts_with("from:") {
                from = Some(extract_email_from_txt_header(line[5..].trim()));
            } else if line.to_lowercase().starts_with("to:") {
                to.push(extract_email_from_txt_header(line[3..].trim()));
            } else if line.to_lowercase().starts_with("cc:") {
                // Add CC recipients to the to list
                to.push(extract_email_from_txt_header(line[3..].trim()));
            } else if line.to_lowercase().starts_with("date:") {
                sent_date = parse_txt_email_date(line[5..].trim());
            }
        } else {
            // Body content
            body_lines.push(line.to_string());
        }
    }

    // Create ParsedEmail with enhanced cleaning
    let body_text = body_lines.join("\n");
    let cleaned_body = clean_txt_email_content(&body_text);

    Ok(ParsedEmail {
        id: Uuid::new_v4(),
        subject,
        from,
        to,
        sent_date,
        plain_text_body_raw: Some(body_text),
        html_body_raw: None,
        cleaned_plain_text_body: Some(cleaned_body),
        folder_type: None, // Will be set later based on filename
    })
}

/// Extract email address from .txt header field
fn extract_email_from_txt_header(header_value: &str) -> String {
    // Look for email in angle brackets first
    if let Some(start) = header_value.find('<') {
        if let Some(end) = header_value.find('>') {
            if end > start {
                return header_value[start + 1..end].trim().to_string();
            }
        }
    }

    // If no angle brackets, look for @ symbol
    if header_value.contains('@') {
        // Take the first word that contains @
        for word in header_value.split_whitespace() {
            if word.contains('@') {
                return word.trim_matches(|c: char| !c.is_ascii_alphanumeric() && c != '@' && c != '.' && c != '-' && c != '_').to_string();
            }
        }
    }

    // Fallback: return the whole value cleaned up
    header_value.trim().to_string()
}

/// Parse email date from .txt format
fn parse_txt_email_date(date_str: &str) -> Option<DateTime<Utc>> {
    use chrono::DateTime;

    // Try various date formats commonly found in Thunderbird exports
    let formats = [
        "%d/%m/%Y, %H:%M %p",  // "11/7/2025, 9:57 μ.μ."
        "%m/%d/%Y, %H:%M %p",  // "7/11/2025, 9:57 PM"
        "%a, %d %b %Y %H:%M:%S %z", // RFC 2822
        "%Y-%m-%d %H:%M:%S",   // ISO-like format
        "%d %b %Y %H:%M:%S",   // "11 Jul 2025 21:56:16"
    ];

    for format in &formats {
        if let Ok(dt) = DateTime::parse_from_str(date_str.trim(), format) {
            return Some(dt.with_timezone(&Utc));
        }
    }

    // Try parsing without timezone
    let naive_formats = [
        "%d/%m/%Y, %H:%M",
        "%m/%d/%Y, %H:%M",
        "%Y-%m-%d %H:%M:%S",
        "%d %b %Y %H:%M:%S",
    ];

    for format in &naive_formats {
        if let Ok(naive_dt) = chrono::NaiveDateTime::parse_from_str(date_str.trim(), format) {
            return Some(DateTime::from_naive_utc_and_offset(naive_dt, Utc));
        }
    }

    None
}

/// Helper function to parse email from bytes using mail-parser crate
/// Extracts only text content, filtering out binary attachments and non-text parts
/// This preserves the original encoding and lets mail-parser handle character set detection
fn parse_email_from_bytes(content: &[u8]) -> Result<ParsedEmail, io::Error> {
    // Use panic-safe parsing for both initial parsing and content extraction
    parse_email_from_bytes_safe(content)
}

/// Helper function to parse email from string content using mail-parser crate
/// Extracts only text content, filtering out binary attachments and non-text parts
fn parse_email_from_string(content: &str) -> Result<ParsedEmail, io::Error> {
    // Use panic-safe parsing for both initial parsing and content extraction
    parse_email_from_bytes_safe(content.as_bytes())
}

/// Comprehensive panic-safe email parsing that protects both initial parsing and content extraction
fn parse_email_from_bytes_safe(content: &[u8]) -> Result<ParsedEmail, io::Error> {
    // 🔍 PRE-PROCESSING: Handle S/MIME emails by extracting text content only
    let content_str = String::from_utf8_lossy(content);
    if content_str.contains("Content-Type: multipart/signed") &&
       content_str.contains("application/pkcs7-signature") {
        // Extract text content from S/MIME email without parsing the signature
        return extract_text_from_smime_email(content);
    }

    // Use catch_unwind to handle panics from mail-parser during initial parsing
    match std::panic::catch_unwind(std::panic::AssertUnwindSafe(|| {
        // Parse the email using mail-parser with full encoding support
        let message = MessageParser::default()
            .parse(content)
            .ok_or_else(|| io::Error::new(io::ErrorKind::InvalidData, "Failed to parse email message"))?;

        // Use panic-safe content extraction
        parse_message_to_email_safe(message)
    })) {
        Ok(result) => result,
        Err(panic_info) => {
            // Convert panic to a recoverable error
            let panic_msg = if let Some(s) = panic_info.downcast_ref::<&str>() {
                s.to_string()
            } else if let Some(s) = panic_info.downcast_ref::<String>() {
                s.clone()
            } else {
                "Unknown panic in mail-parser during initial parsing".to_string()
            };

            eprintln!("Warning: mail-parser panic caught during initial parsing: {}", panic_msg);
            Err(io::Error::new(
                io::ErrorKind::InvalidData,
                format!("Email parsing failed due to mail-parser panic during initial parsing: {}", panic_msg)
            ))
        }
    }
}

/// Panic-safe wrapper for parse_message_to_email that catches mail-parser panics during content extraction
fn parse_message_to_email_safe(message: mail_parser::Message) -> Result<ParsedEmail, io::Error> {
    // Use catch_unwind to handle panics from mail-parser when processing malformed multipart emails
    match std::panic::catch_unwind(std::panic::AssertUnwindSafe(|| {
        parse_message_to_email(message)
    })) {
        Ok(result) => result,
        Err(panic_info) => {
            // Convert panic to a recoverable error
            let panic_msg = if let Some(s) = panic_info.downcast_ref::<&str>() {
                s.to_string()
            } else if let Some(s) = panic_info.downcast_ref::<String>() {
                s.clone()
            } else {
                "Unknown panic in mail-parser".to_string()
            };

            eprintln!("Warning: mail-parser panic caught during content extraction: {}", panic_msg);
            Err(io::Error::new(
                io::ErrorKind::InvalidData,
                format!("Email parsing failed due to mail-parser panic during content extraction: {}", panic_msg)
            ))
        }
    }
}

/// Common function to extract data from a parsed mail-parser Message
fn parse_message_to_email(message: mail_parser::Message) -> Result<ParsedEmail, io::Error> {

    // Extract basic header information
    let subject = message.subject().map(|s| s.to_string());
    let from = message.from()
        .and_then(|addr_list| addr_list.first())
        .and_then(|addr| addr.address())
        .map(|s| s.to_string());

    let mut to = Vec::new();
    if let Some(to_addrs) = message.to() {
        for addr in to_addrs.iter() {
            if let Some(address) = addr.address() {
                to.push(address.to_string());
            }
        }
    }

    let sent_date = message.date().map(|dt| {
        // Convert mail-parser DateTime to chrono DateTime<Utc>
        DateTime::<Utc>::from_timestamp(dt.to_timestamp(), 0).unwrap_or_else(|| Utc::now())
    });

    // Extract X-Folder-Type header if present (added by mail cleaner)
    let folder_type = message.header("X-Folder-Type")
        .and_then(|header| header.as_text())
        .map(|s| s.to_string());

    // Extract text content only - this automatically excludes binary attachments
    let mut plain_text_parts = Vec::new();
    let mut html_parts = Vec::new();

    // Collect all text body parts (excludes attachments and binary content)
    for i in 0..message.text_body_count() {
        if let Some(text) = message.body_text(i) {
            plain_text_parts.push(text.to_string());
        }
    }

    // Collect all HTML body parts and convert to plain text
    for i in 0..message.html_body_count() {
        if let Some(html) = message.body_html(i) {
            html_parts.push(html.to_string());
        }
    }

    // Combine plain text parts
    let plain_text_body_raw = if !plain_text_parts.is_empty() {
        Some(plain_text_parts.join("\n\n"))
    } else {
        None
    };

    // Combine HTML parts
    let html_body_raw = if !html_parts.is_empty() {
        Some(html_parts.join("\n\n"))
    } else {
        None
    };

    // Create cleaned plain text body
    let cleaned_plain_text_body = if let Some(html) = &html_body_raw {
        // Convert HTML to plain text and clean it
        let plain_from_html = html_to_plain_text(html);
        Some(strip_signatures_and_quotes(&plain_from_html))
    } else if let Some(plain) = &plain_text_body_raw {
        // Clean the plain text
        Some(strip_signatures_and_quotes(plain))
    } else {
        None
    };

    Ok(ParsedEmail {
        id: Uuid::new_v4(),
        subject,
        from,
        to,
        sent_date,
        plain_text_body_raw,
        html_body_raw,
        cleaned_plain_text_body,
        folder_type,
    })
}

/// Establishes a connection to Qdrant vector database.
pub async fn establish_connection() -> Result<Qdrant, Box<dyn std::error::Error + Send + Sync>> {
    dotenv().ok(); // Load .env file if present

    let qdrant_url = std::env::var("QDRANT_URL")
        .unwrap_or_else(|_| "http://localhost:6334".to_string());

    let client = Qdrant::from_url(&qdrant_url).build()?;
    Ok(client)
}

/// Sets up Qdrant collections for email storage.
pub async fn setup_collections(client: &Qdrant) -> Result<(), Box<dyn std::error::Error + Send + Sync>> {
    let collection_name = "emails";

    // Check if collection exists, create if not
    match client.collection_info(collection_name).await {
        Ok(_) => {
            println!("✓ Collection '{}' already exists", collection_name);
        }
        Err(_) => {
            println!("Creating collection '{}'...", collection_name);

            let create_collection = CreateCollection {
                collection_name: collection_name.to_string(),
                vectors_config: Some(VectorParams {
                    size: 1024, // intfloat/e5-large-v2 dimension
                    distance: Distance::Cosine as i32,
                    ..Default::default()
                }.into()),
                ..Default::default()
            };

            client.create_collection(create_collection).await?;
            println!("✓ Collection '{}' created successfully", collection_name);

            // Note: Qdrant automatically creates indexes for payload fields as needed
            // The enhanced metadata fields will be indexed when first used in queries
            println!("✓ Collection '{}' is ready for enhanced metadata storage", collection_name);
        }
    }

    Ok(())
}

/// Get current memory usage in MB (Windows-specific)
#[cfg(target_os = "windows")]
pub fn get_current_memory_usage_mb() -> f64 {
    use std::mem;

    unsafe {
        let handle = winapi::um::processthreadsapi::GetCurrentProcess();
        let mut pmc: winapi::um::psapi::PROCESS_MEMORY_COUNTERS = mem::zeroed();

        if winapi::um::psapi::GetProcessMemoryInfo(
            handle,
            &mut pmc as *mut _,
            mem::size_of::<winapi::um::psapi::PROCESS_MEMORY_COUNTERS>() as u32,
        ) != 0 {
            pmc.WorkingSetSize as f64 / (1024.0 * 1024.0)
        } else {
            0.0
        }
    }
}

/// Get current memory usage in MB (non-Windows fallback)
#[cfg(not(target_os = "windows"))]
pub fn get_current_memory_usage_mb() -> f64 {
    0.0
}

/// Extract ONLY text content from S/MIME emails, aggressively stripping ALL binary data
fn extract_text_from_smime_email(content: &[u8]) -> Result<ParsedEmail, io::Error> {
    let content_str = String::from_utf8_lossy(content);
    let lines: Vec<&str> = content_str.lines().collect();

    let mut subject = None;
    let mut from = None;
    let mut to = Vec::new();
    let mut sent_date = None;
    let mut text_content = String::new();
    let mut in_headers = true;
    let mut skip_until_boundary = false;
    let mut current_boundary = None;

    for line in lines {
        if in_headers {
            // Parse email headers only
            if line.trim().is_empty() {
                in_headers = false;
                continue;
            }

            if line.to_lowercase().starts_with("subject:") {
                subject = Some(line[8..].trim().to_string());
            } else if line.to_lowercase().starts_with("from:") {
                from = Some(extract_email_from_header(line[5..].trim()));
            } else if line.to_lowercase().starts_with("to:") {
                to.push(extract_email_from_header(line[3..].trim()));
            } else if line.to_lowercase().starts_with("date:") {
                sent_date = parse_email_date(line[5..].trim());
            } else if line.to_lowercase().contains("boundary=") {
                // Extract boundary for MIME part detection
                if let Some(start) = line.find("boundary=") {
                    let boundary_part = &line[start + 9..];
                    current_boundary = Some(boundary_part.trim_matches('"').trim_matches('\'').to_string());
                }
            }
        } else {
            // AGGRESSIVE BINARY FILTERING in body

            // Skip ALL signature-related content
            if line.contains("pkcs7") || line.contains("signature") ||
               line.contains("application/") || line.contains("smime") {
                skip_until_boundary = true;
                continue;
            }

            // Skip ALL base64 content (any line that looks like base64)
            if line.len() > 40 && line.chars().all(|c| c.is_ascii_alphanumeric() || c == '+' || c == '/' || c == '=') {
                continue;
            }

            // Skip ALL Content-Transfer-Encoding: base64 sections
            if line.to_lowercase().contains("content-transfer-encoding: base64") {
                skip_until_boundary = true;
                continue;
            }

            // Skip ALL attachment-related content
            if line.to_lowercase().contains("attachment") ||
               line.to_lowercase().contains("content-disposition:") {
                skip_until_boundary = true;
                continue;
            }

            // Reset skip flag on MIME boundaries
            if let Some(ref boundary) = current_boundary {
                if line.contains(boundary) {
                    skip_until_boundary = false;
                    continue;
                }
            }

            // Also reset on any boundary-like line
            if line.starts_with("--") && line.len() > 10 {
                skip_until_boundary = false;
                continue;
            }

            // Skip if we're in a binary section
            if skip_until_boundary {
                continue;
            }

            // Skip ALL Content-* headers in body
            if line.to_lowercase().starts_with("content-") {
                continue;
            }

            // Skip MIME version headers
            if line.to_lowercase().starts_with("mime-version:") {
                continue;
            }

            // ONLY collect lines that look like actual text content
            if !line.trim().is_empty() &&
               !line.starts_with("--") &&  // Not boundary
               !line.contains("=") ||      // Not quoted-printable encoded
               line.contains(" ") {        // Contains spaces (likely text)

                // Additional filtering: skip lines with too many special chars
                let special_char_count = line.chars().filter(|c| !c.is_ascii_alphanumeric() && !c.is_whitespace()).count();
                let total_chars = line.len();

                // Only keep lines where special chars are < 30% of total
                if total_chars > 0 && (special_char_count as f32 / total_chars as f32) < 0.3 {
                    text_content.push_str(line);
                    text_content.push('\n');
                }
            }
        }
    }

    // Aggressively clean the text content
    let cleaned_text = clean_quoted_printable(&text_content)
        .lines()
        .filter(|line| {
            // Remove lines that still look like encoded content
            !line.trim().is_empty() &&
            !line.contains("=EF=BF=BD") &&
            line.len() < 200 &&  // Skip very long lines (likely encoded)
            line.chars().filter(|c| c.is_ascii_alphabetic()).count() > 3  // Must have some letters
        })
        .collect::<Vec<_>>()
        .join("\n");

    Ok(ParsedEmail {
        id: Uuid::new_v4(),
        subject,
        from,
        to,
        sent_date,
        plain_text_body_raw: if !cleaned_text.trim().is_empty() { Some(cleaned_text.clone()) } else { None },
        html_body_raw: None,
        cleaned_plain_text_body: if !cleaned_text.trim().is_empty() { Some(cleaned_text) } else { None },
        folder_type: None, // S/MIME extraction doesn't have folder type info
    })
}

/// Parse S/MIME signed emails by extracting only text content, avoiding binary signatures
fn parse_smime_email_text_only(content: &[u8]) -> Result<ParsedEmail, io::Error> {
    let content_str = String::from_utf8_lossy(content);
    let lines: Vec<&str> = content_str.lines().collect();

    let mut subject = None;
    let mut from = None;
    let mut to = Vec::new();
    let mut sent_date = None;
    let mut plain_text_parts = Vec::new();
    let mut html_text_parts = Vec::new();

    let mut in_headers = true;
    let mut in_text_part = false;
    let mut in_html_part = false;
    let mut current_text = String::new();
    let mut skip_binary_part = false;

    for line in lines {
        if in_headers {
            // Parse headers
            if line.trim().is_empty() {
                in_headers = false;
                continue;
            }

            if line.to_lowercase().starts_with("subject:") {
                subject = Some(line[8..].trim().to_string());
            } else if line.to_lowercase().starts_with("from:") {
                from = Some(extract_email_address(line[5..].trim()));
            } else if line.to_lowercase().starts_with("to:") {
                to.push(extract_email_address(line[3..].trim()));
            } else if line.to_lowercase().starts_with("date:") {
                sent_date = parse_date_header(line[5..].trim());
            }
        } else {
            // Process MIME parts, but skip binary content
            if line.starts_with("------=_NextPart_") {
                // Save current text part
                if in_text_part && !current_text.trim().is_empty() {
                    plain_text_parts.push(current_text.clone());
                }
                if in_html_part && !current_text.trim().is_empty() {
                    html_text_parts.push(current_text.clone());
                }

                current_text.clear();
                in_text_part = false;
                in_html_part = false;
                skip_binary_part = false;
                continue;
            }

            // Detect content types
            if line.to_lowercase().starts_with("content-type:") {
                if line.contains("text/plain") {
                    in_text_part = true;
                    skip_binary_part = false;
                } else if line.contains("text/html") {
                    in_html_part = true;
                    skip_binary_part = false;
                } else if line.contains("application/") || line.contains("pkcs7") {
                    // Skip binary parts like S/MIME signatures
                    skip_binary_part = true;
                    in_text_part = false;
                    in_html_part = false;
                }
                continue;
            }

            // Skip binary content and base64 blocks
            if skip_binary_part ||
               line.starts_with("Content-Transfer-Encoding: base64") ||
               (line.len() > 60 && line.chars().all(|c| c.is_ascii_alphanumeric() || c == '+' || c == '/' || c == '=')) {
                continue;
            }

            // Collect text content
            if (in_text_part || in_html_part) && !line.starts_with("Content-") {
                current_text.push_str(line);
                current_text.push('\n');
            }
        }
    }

    // Save final text part
    if in_text_part && !current_text.trim().is_empty() {
        plain_text_parts.push(current_text.clone());
    }
    if in_html_part && !current_text.trim().is_empty() {
        html_text_parts.push(current_text);
    }

    // Combine text parts
    let plain_text_body_raw = if !plain_text_parts.is_empty() {
        Some(plain_text_parts.join("\n\n"))
    } else {
        None
    };

    let html_body_raw = if !html_text_parts.is_empty() {
        Some(html_text_parts.join("\n\n"))
    } else {
        None
    };

    // Clean the text
    let cleaned_plain_text_body = plain_text_body_raw.as_ref()
        .map(|text| clean_email_text(text));

    Ok(ParsedEmail {
        id: Uuid::new_v4(),
        subject,
        from,
        to,
        sent_date,
        plain_text_body_raw,
        html_body_raw,
        cleaned_plain_text_body,
        folder_type: None, // This function doesn't have folder type info
    })
}

/// Estimate the memory size of a parsed email in MB
fn estimate_parsed_email_size(email: &ParsedEmail) -> f64 {
    let mut size_bytes = 0;

    // String fields
    size_bytes += email.subject.as_ref().map_or(0, |s| s.len());
    size_bytes += email.from.as_ref().map_or(0, |s| s.len());

    // Vector fields
    for to_addr in &email.to {
        size_bytes += to_addr.len();
    }

    // Body fields (these are likely the biggest memory consumers)
    size_bytes += email.plain_text_body_raw.as_ref().map_or(0, |s| s.len());
    size_bytes += email.html_body_raw.as_ref().map_or(0, |s| s.len());
    size_bytes += email.cleaned_plain_text_body.as_ref().map_or(0, |s| s.len());

    // Convert to MB
    size_bytes as f64 / (1024.0 * 1024.0)
}

/// Extract email address from header field (handles "Name <<EMAIL>>" format)
fn extract_email_address(header_value: &str) -> String {
    // Look for email in angle brackets first
    if let Some(start) = header_value.find('<') {
        if let Some(end) = header_value.find('>') {
            if end > start {
                return header_value[start + 1..end].trim().to_string();
            }
        }
    }

    // If no angle brackets, look for @ symbol
    if header_value.contains('@') {
        // Take the first word that contains @
        for word in header_value.split_whitespace() {
            if word.contains('@') {
                return word.trim_matches(|c: char| !c.is_ascii_alphanumeric() && c != '@' && c != '.' && c != '-' && c != '_').to_string();
            }
        }
    }

    // Fallback: return the whole value cleaned up
    header_value.trim().to_string()
}

/// Parse date header using chrono
fn parse_date_header(date_str: &str) -> Option<DateTime<Utc>> {
    use chrono::DateTime;

    // Try common email date formats
    let formats = [
        "%a, %d %b %Y %H:%M:%S %z",      // RFC 2822
        "%d %b %Y %H:%M:%S %z",          // Without day name
        "%a, %d %b %Y %H:%M:%S %Z",      // With timezone name
        "%d %b %Y %H:%M:%S %Z",          // Without day name, timezone name
        "%Y-%m-%d %H:%M:%S %z",          // ISO-like with timezone
        "%a, %d %b %Y %H:%M:%S",         // Without timezone
    ];

    for format in &formats {
        if let Ok(dt) = DateTime::parse_from_str(date_str.trim(), format) {
            return Some(dt.with_timezone(&Utc));
        }
    }

    None
}

/// Clean email text by removing quoted-printable encoding, quoted content, and extra whitespace
fn clean_email_text(text: &str) -> String {
    let mut cleaned = text
        .replace("=EF=BF=BD", "") // Remove replacement characters
        .replace("=20", " ")       // Quoted-printable space
        .replace("=0D=0A", "\n")   // Quoted-printable line breaks
        .replace("=3D", "=")       // Quoted-printable equals
        .replace("=\n", "")        // Soft line breaks
        .replace("=\r\n", "");     // Soft line breaks with CRLF

    // Remove quoted content and signatures
    cleaned = remove_quoted_content(&cleaned);

    // Remove excessive whitespace
    while cleaned.contains("\n\n\n") {
        cleaned = cleaned.replace("\n\n\n", "\n\n");
    }

    cleaned.trim().to_string()
}

/// Remove quoted content from email text (replies, forwards, signatures)
fn remove_quoted_content(text: &str) -> String {
    let lines: Vec<&str> = text.lines().collect();
    let mut cleaned_lines = Vec::new();
    let mut i = 0;

    while i < lines.len() {
        let line = lines[i].trim();

        // Check for various quoted content patterns
        if is_quoted_content_start(line, &lines, i) {
            // Skip the rest of the email (quoted content)
            break;
        }

        // Check for signature patterns
        if is_signature_start(line) {
            // Skip signatures but continue processing (there might be more content after)
            i = skip_signature(&lines, i);
            continue;
        }

        // Check for traditional quoted lines (starting with >)
        if line.starts_with('>') {
            i += 1;
            continue;
        }

        // Keep the line if it's not quoted content
        cleaned_lines.push(lines[i]);
        i += 1;
    }

    cleaned_lines.join("\n")
}

/// Check if a line indicates the start of quoted content
fn is_quoted_content_start(line: &str, lines: &[&str], index: usize) -> bool {
    let line_lower = line.to_lowercase();

    // Pattern 1: "On [date], [person] wrote:" or "On [date] [person] wrote:"
    if line_lower.contains("wrote:") && (line_lower.contains("on ") || line_lower.contains("at ")) {
        return true;
    }

    // Pattern 2: "From: [email]" followed by "Sent:" or "Date:" within next few lines
    if line_lower.starts_with("from:") && line_lower.contains("@") {
        // Check next few lines for "Sent:" or "Date:"
        for j in (index + 1)..std::cmp::min(index + 5, lines.len()) {
            let next_line = lines[j].to_lowercase();
            if next_line.starts_with("sent:") || next_line.starts_with("date:") || next_line.starts_with("to:") {
                return true;
            }
        }
    }

    // Pattern 3: "-----Original Message-----" or similar
    if line.contains("-----") && (line_lower.contains("original") || line_lower.contains("message")) {
        return true;
    }

    // Pattern 4: "---------- Forwarded message ----------"
    if line.contains("----------") && line_lower.contains("forward") {
        return true;
    }

    // Pattern 5: Email address followed by colon (like "<EMAIL>:")
    if line.contains("@") && line.ends_with(':') && line.split_whitespace().count() <= 3 {
        return true;
    }

    // Pattern 6: Date patterns that typically start quoted content
    if line_lower.contains("sent:") || line_lower.contains("date:") {
        // Check if previous line looks like "From:"
        if index > 0 {
            let prev_line = lines[index - 1].to_lowercase();
            if prev_line.starts_with("from:") {
                return true;
            }
        }
    }

    false
}

/// Check if a line indicates the start of a signature
fn is_signature_start(line: &str) -> bool {
    // Common signature patterns
    line == "--" ||
    line == "---" ||
    line.starts_with("--") && line.len() <= 10 ||
    line.to_lowercase().contains("regards") ||
    line.to_lowercase().contains("sincerely") ||
    line.to_lowercase().contains("best wishes") ||
    line.to_lowercase().contains("thank you")
}

/// Skip signature lines and return the next index to process
fn skip_signature(lines: &[&str], start_index: usize) -> usize {
    let mut i = start_index;

    // Skip signature lines (usually short lines, contact info, etc.)
    while i < lines.len() {
        let line = lines[i].trim();

        // Stop skipping if we hit a long line that looks like content
        if line.len() > 100 && !line.contains("@") && !line.contains("http") {
            break;
        }

        // Stop if we hit what looks like the start of quoted content
        if is_quoted_content_start(line, lines, i) {
            break;
        }

        i += 1;

        // Don't skip more than 10 lines for signatures
        if i - start_index > 10 {
            break;
        }
    }

    i
}

/// Calculate content similarity between two email bodies using Jaccard similarity
pub fn calculate_content_similarity(content1: &str, content2: &str) -> f32 {
    use std::collections::HashSet;

    // Normalize and tokenize content
    let words1: HashSet<String> = content1
        .to_lowercase()
        .split_whitespace()
        .filter(|word| word.len() > 3 && !word.chars().all(|c| !c.is_alphabetic()))
        .map(|word| word.trim_matches(|c: char| !c.is_alphanumeric()).to_string())
        .filter(|word| !word.is_empty())
        .collect();

    let words2: HashSet<String> = content2
        .to_lowercase()
        .split_whitespace()
        .filter(|word| word.len() > 3 && !word.chars().all(|c| !c.is_alphabetic()))
        .map(|word| word.trim_matches(|c: char| !c.is_alphanumeric()).to_string())
        .filter(|word| !word.is_empty())
        .collect();

    // If either set is too small, don't consider them similar
    if words1.len() < 10 || words2.len() < 10 {
        return 0.0;
    }

    // Calculate Jaccard similarity: |intersection| / |union|
    let intersection_size = words1.intersection(&words2).count();
    let union_size = words1.union(&words2).count();

    if union_size == 0 {
        0.0
    } else {
        intersection_size as f32 / union_size as f32
    }
}

/// Check if an email is likely a duplicate based on content similarity
pub fn is_likely_duplicate(email_content: &str, existing_contents: &[String], similarity_threshold: f32) -> Option<usize> {
    for (index, existing_content) in existing_contents.iter().enumerate() {
        let similarity = calculate_content_similarity(email_content, existing_content);
        if similarity >= similarity_threshold {
            return Some(index);
        }
    }
    None
}

/// Enhanced email cleaning that also removes quoted content from .txt files
pub fn clean_txt_email_content(content: &str) -> String {
    // First apply the standard email text cleaning
    let cleaned = clean_email_text(content);

    // Additional cleaning specific to .txt exports
    let lines: Vec<&str> = cleaned.lines().collect();
    let mut final_lines = Vec::new();

    for line in lines {
        let trimmed = line.trim();

        // Skip empty lines at the beginning
        if final_lines.is_empty() && trimmed.is_empty() {
            continue;
        }

        // Skip lines that look like email client artifacts
        if trimmed.starts_with("--") && trimmed.len() < 20 {
            continue;
        }

        // Skip lines that are just email addresses or timestamps
        if trimmed.contains("@") && trimmed.split_whitespace().count() <= 3 {
            continue;
        }

        final_lines.push(line);
    }

    final_lines.join("\n").trim().to_string()
}

/// Write a parsed email as text-only content to an mbox file
fn write_text_only_email_to_file(output_file: &mut std::fs::File, email: &ParsedEmail) -> Result<(), io::Error> {
    use std::io::Write;

    // Write mbox separator
    let date_str = email.sent_date.map_or("Unknown".to_string(), |d| d.to_rfc2822());
    let from_str = email.from.as_ref().map_or("<EMAIL>", |f| f.as_str());
    writeln!(output_file, "From {} {}", from_str, date_str)?;

    // Write essential headers only
    if let Some(ref subject) = email.subject {
        writeln!(output_file, "Subject: {}", subject)?;
    }
    if let Some(ref from) = email.from {
        writeln!(output_file, "From: {}", from)?;
    }
    if !email.to.is_empty() {
        writeln!(output_file, "To: {}", email.to.join(", "))?;
    }
    if let Some(ref date) = email.sent_date {
        writeln!(output_file, "Date: {}", date.to_rfc2822())?;
    }

    // Add folder type header if available
    if let Some(ref folder_type) = email.folder_type {
        writeln!(output_file, "X-Folder-Type: {}", folder_type)?;
    }

    // Add minimal MIME headers for text content
    writeln!(output_file, "Content-Type: text/plain; charset=utf-8")?;
    writeln!(output_file, "Content-Transfer-Encoding: 8bit")?;
    writeln!(output_file)?; // Empty line between headers and body

    // Write ONLY the cleaned text content
    if let Some(ref text) = email.cleaned_plain_text_body {
        writeln!(output_file, "{}", text)?;
    } else if let Some(ref text) = email.plain_text_body_raw {
        writeln!(output_file, "{}", text)?;
    } else {
        writeln!(output_file, "[No text content extracted]")?;
    }

    writeln!(output_file)?; // Empty line after email

    Ok(())
}

/// Extract email address from header (simple version for S/MIME processing)
fn extract_email_from_header(header_value: &str) -> String {
    // Look for email in angle brackets first
    if let Some(start) = header_value.find('<') {
        if let Some(end) = header_value.find('>') {
            if end > start {
                return header_value[start + 1..end].trim().to_string();
            }
        }
    }

    // Fallback: return cleaned header value
    header_value.trim().to_string()
}

/// Parse email date (simple version for S/MIME processing)
fn parse_email_date(date_str: &str) -> Option<DateTime<Utc>> {
    use chrono::DateTime;

    // Try basic RFC 2822 format
    if let Ok(dt) = DateTime::parse_from_str(date_str.trim(), "%a, %d %b %Y %H:%M:%S %z") {
        return Some(dt.with_timezone(&Utc));
    }

    None
}

/// Clean quoted-printable encoding from text
fn clean_quoted_printable(text: &str) -> String {
    text.replace("=EF=BF=BD", "") // Remove replacement characters
        .replace("=20", " ")       // Quoted-printable space
        .replace("=0D=0A", "\n")   // Quoted-printable line breaks
        .replace("=3D", "=")       // Quoted-printable equals
        .replace("=\n", "")        // Soft line breaks
        .replace("=\r\n", "")      // Soft line breaks with CRLF
        .trim()
        .to_string()
}

/// Inserts a message with embedding into Qdrant.
pub async fn insert_message_with_embedding(
    client: &Qdrant,
    message: &Message,
    embedding: Vec<f32>
) -> Result<Uuid, Box<dyn std::error::Error + Send + Sync>> {
    let collection_name = "emails";

    // Create payload with message metadata
    let mut payload = HashMap::new();
    payload.insert("id".to_string(), Value::from(message.id.to_string()));
    payload.insert("subject".to_string(), Value::from(message.subject.clone().unwrap_or_default()));
    payload.insert("from_address".to_string(), Value::from(message.from_address.clone().unwrap_or_default()));
    payload.insert("to_addresses".to_string(), Value::from(serde_json::to_string(&message.to_addresses)?));
    payload.insert("sent_date".to_string(), Value::from(message.sent_date.map(|d| d.to_rfc3339()).unwrap_or_default()));
    payload.insert("plain_text_body_raw".to_string(), Value::from(message.plain_text_body_raw.clone().unwrap_or_default()));
    payload.insert("html_body_raw".to_string(), Value::from(message.html_body_raw.clone().unwrap_or_default()));
    payload.insert("cleaned_plain_text_body".to_string(), Value::from(message.cleaned_plain_text_body.clone().unwrap_or_default()));
    payload.insert("file_path".to_string(), Value::from(message.file_path.clone().unwrap_or_default()));
    payload.insert("created_at".to_string(), Value::from(message.created_at.to_rfc3339()));
    payload.insert("updated_at".to_string(), Value::from(message.updated_at.to_rfc3339()));

    // Add threading metadata
    payload.insert("thread_id".to_string(), Value::from(message.thread_id.clone().unwrap_or_default()));
    payload.insert("conversation_id".to_string(), Value::from(message.conversation_id.clone().unwrap_or_default()));
    payload.insert("thread_position".to_string(), Value::from(message.thread_position.unwrap_or(0).to_string()));
    payload.insert("email_weight".to_string(), Value::from(message.email_weight.unwrap_or(1.0).to_string()));
    payload.insert("email_type".to_string(), Value::from(message.email_type.clone().unwrap_or_default()));
    payload.insert("is_duplicate".to_string(), Value::from(message.is_duplicate.unwrap_or(false).to_string()));

    // Add message headers
    payload.insert("message_id".to_string(), Value::from(message.message_id.clone().unwrap_or_default()));
    payload.insert("in_reply_to".to_string(), Value::from(serde_json::to_string(&message.in_reply_to)?));
    payload.insert("references".to_string(), Value::from(serde_json::to_string(&message.references)?));

    // Add content analysis
    payload.insert("content_hash".to_string(), Value::from(message.content_hash.clone().unwrap_or_default()));
    payload.insert("normalized_subject".to_string(), Value::from(message.normalized_subject.clone().unwrap_or_default()));

    // Add case cataloging metadata
    payload.insert("case_id".to_string(), Value::from(message.case_id.clone().unwrap_or_default()));
    payload.insert("case_subject".to_string(), Value::from(message.case_subject.clone().unwrap_or_default()));
    payload.insert("case_participants".to_string(), Value::from(serde_json::to_string(&message.case_participants)?));

    // Add country identification
    payload.insert("country_uuid".to_string(), Value::from(message.country_uuid.clone().unwrap_or_default()));

    // Create point with vector and payload
    let point = PointStruct::new(
        message.id.to_string(),
        embedding,
        payload,
    );

    // Upsert point to collection
    let upsert_request = UpsertPoints {
        collection_name: collection_name.to_string(),
        points: vec![point],
        ..Default::default()
    };
    client.upsert_points(upsert_request).await?;

    Ok(message.id)
}

/// Searches for similar messages using vector similarity.
pub async fn search_similar_messages(
    client: &Qdrant,
    query_embedding: Vec<f32>,
    limit: Option<u64>,
    score_threshold: Option<f32>
) -> Result<Vec<SearchResult>, Box<dyn std::error::Error + Send + Sync>> {
    let collection_name = "emails";
    let limit = limit.unwrap_or(10);

    let search_result = client.search_points(SearchPoints {
        collection_name: collection_name.to_string(),
        vector: query_embedding,
        limit,
        score_threshold,
        with_payload: Some(WithPayloadSelector {
            selector_options: Some(qdrant_client::qdrant::with_payload_selector::SelectorOptions::Enable(true)),
        }),
        with_vectors: Some(WithVectorsSelector {
            selector_options: Some(qdrant_client::qdrant::with_vectors_selector::SelectorOptions::Enable(false)),
        }),
        ..Default::default()
    }).await?;

    let mut results = Vec::new();
    for scored_point in search_result.result {
        let payload = scored_point.payload;
            let id_str = payload.get("id").unwrap().to_string();
            let subject_str = payload.get("subject").unwrap().to_string();
            let from_str = payload.get("from_address").unwrap().to_string();
            let to_addresses_str = payload.get("to_addresses").unwrap().to_string();
            let sent_date_str = payload.get("sent_date").unwrap().to_string();
            let plain_text_str = payload.get("plain_text_body_raw").unwrap().to_string();
            let html_str = payload.get("html_body_raw").unwrap().to_string();
            let cleaned_str = payload.get("cleaned_plain_text_body").unwrap().to_string();
            let file_path_str = payload.get("file_path").unwrap().to_string();
            let created_at_str = payload.get("created_at").unwrap().to_string();
            let updated_at_str = payload.get("updated_at").unwrap().to_string();

            // Extract threading metadata with safe defaults
            let thread_id_str = payload.get("thread_id").map(|v| v.to_string()).unwrap_or_default();
            let conversation_id_str = payload.get("conversation_id").map(|v| v.to_string()).unwrap_or_default();
            let thread_position_str = payload.get("thread_position").map(|v| v.to_string()).unwrap_or_default();
            let email_weight_str = payload.get("email_weight").map(|v| v.to_string()).unwrap_or_default();
            let email_type_str = payload.get("email_type").map(|v| v.to_string()).unwrap_or_default();
            let is_duplicate_str = payload.get("is_duplicate").map(|v| v.to_string()).unwrap_or_default();

            // Extract message headers
            let message_id_str = payload.get("message_id").map(|v| v.to_string()).unwrap_or_default();
            let in_reply_to_str = payload.get("in_reply_to").map(|v| v.to_string()).unwrap_or_default();
            let references_str = payload.get("references").map(|v| v.to_string()).unwrap_or_default();

            // Extract content analysis
            let content_hash_str = payload.get("content_hash").map(|v| v.to_string()).unwrap_or_default();
            let normalized_subject_str = payload.get("normalized_subject").map(|v| v.to_string()).unwrap_or_default();

            // Extract case cataloging
            let case_id_str = payload.get("case_id").map(|v| v.to_string()).unwrap_or_default();
            let case_subject_str = payload.get("case_subject").map(|v| v.to_string()).unwrap_or_default();
            let case_participants_str = payload.get("case_participants").map(|v| v.to_string()).unwrap_or_default();

            // Extract country identification
            let country_uuid_str = payload.get("country_uuid").map(|v| v.to_string()).unwrap_or_default();

            let message = Message {
                id: Uuid::parse_str(&id_str.trim_matches('"'))?,
                subject: Some(subject_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                from_address: Some(from_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                to_addresses: safe_parse_json_array(&to_addresses_str)?,
                sent_date: Some(sent_date_str.trim_matches('"'))
                    .filter(|s| !s.is_empty())
                    .and_then(|s| DateTime::parse_from_rfc3339(&s).ok())
                    .map(|dt| dt.with_timezone(&Utc)),
                plain_text_body_raw: Some(plain_text_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                html_body_raw: Some(html_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                cleaned_plain_text_body: Some(cleaned_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                file_path: Some(file_path_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                created_at: DateTime::parse_from_rfc3339(&created_at_str.trim_matches('"'))?.with_timezone(&Utc),
                updated_at: DateTime::parse_from_rfc3339(&updated_at_str.trim_matches('"'))?.with_timezone(&Utc),

                // Threading metadata
                thread_id: Some(thread_id_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                conversation_id: Some(conversation_id_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                thread_position: thread_position_str.trim_matches('"').parse::<u32>().ok(),
                email_weight: email_weight_str.trim_matches('"').parse::<f32>().ok(),
                email_type: Some(email_type_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                is_duplicate: is_duplicate_str.trim_matches('"').parse::<bool>().ok(),

                // Message headers
                message_id: Some(message_id_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                in_reply_to: safe_parse_json_array(&in_reply_to_str).unwrap_or_default(),
                references: safe_parse_json_array(&references_str).unwrap_or_default(),

                // Content analysis
                content_hash: Some(content_hash_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                normalized_subject: Some(normalized_subject_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),

                // Case cataloging
                case_id: Some(case_id_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                case_subject: Some(case_subject_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                case_participants: safe_parse_json_array(&case_participants_str).unwrap_or_default(),

                // Country identification
                country_uuid: Some(country_uuid_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
            };

        results.push(SearchResult {
            message,
            score: scored_point.score,
        });
    }

    Ok(results)
}

/// Gets recent messages (without vector search).
/// When no limit is specified, returns last 200 emails (prioritizing received emails).
pub async fn get_recent_messages(
    client: &Qdrant,
    limit: Option<u64>
) -> Result<Vec<Message>, Box<dyn std::error::Error + Send + Sync>> {
    let collection_name = "emails";

    // Fetch more messages than needed to allow filtering and sorting
    let mut all_messages = Vec::new();
    let mut offset = None;
    let target_limit = limit.unwrap_or(200); // Default to 200 emails
    let fetch_limit = if limit.is_none() { 1000 } else { target_limit }; // Fetch more to filter

    loop {
        let scroll_request = ScrollPoints {
            collection_name: collection_name.to_string(),
            limit: Some(std::cmp::min(fetch_limit, 1000) as u32), // Batch size
            offset,
            with_payload: Some(WithPayloadSelector {
                selector_options: Some(qdrant_client::qdrant::with_payload_selector::SelectorOptions::Enable(true)),
            }),
            with_vectors: Some(WithVectorsSelector {
                selector_options: Some(qdrant_client::qdrant::with_vectors_selector::SelectorOptions::Enable(false)),
            }),
            ..Default::default()
        };
        let scroll_result = client.scroll(scroll_request).await?;

        for point in scroll_result.result {
            let payload = point.payload;
            let id_str = payload.get("id").unwrap().to_string();
            let subject_str = payload.get("subject").unwrap().to_string();
            let from_str = payload.get("from_address").unwrap().to_string();
            let to_addresses_str = payload.get("to_addresses").unwrap().to_string();
            let sent_date_str = payload.get("sent_date").unwrap().to_string();
            let plain_text_str = payload.get("plain_text_body_raw").unwrap().to_string();
            let html_str = payload.get("html_body_raw").unwrap().to_string();
            let cleaned_str = payload.get("cleaned_plain_text_body").unwrap().to_string();
            let file_path_str = payload.get("file_path").unwrap().to_string();
            let created_at_str = payload.get("created_at").unwrap().to_string();
            let updated_at_str = payload.get("updated_at").unwrap().to_string();

            // Extract threading metadata with safe defaults
            let thread_id_str = payload.get("thread_id").map(|v| v.to_string()).unwrap_or_default();
            let conversation_id_str = payload.get("conversation_id").map(|v| v.to_string()).unwrap_or_default();
            let thread_position_str = payload.get("thread_position").map(|v| v.to_string()).unwrap_or_default();
            let email_weight_str = payload.get("email_weight").map(|v| v.to_string()).unwrap_or_default();
            let email_type_str = payload.get("email_type").map(|v| v.to_string()).unwrap_or_default();
            let is_duplicate_str = payload.get("is_duplicate").map(|v| v.to_string()).unwrap_or_default();

            // Extract message headers
            let message_id_str = payload.get("message_id").map(|v| v.to_string()).unwrap_or_default();
            let in_reply_to_str = payload.get("in_reply_to").map(|v| v.to_string()).unwrap_or_default();
            let references_str = payload.get("references").map(|v| v.to_string()).unwrap_or_default();

            // Extract content analysis
            let content_hash_str = payload.get("content_hash").map(|v| v.to_string()).unwrap_or_default();
            let normalized_subject_str = payload.get("normalized_subject").map(|v| v.to_string()).unwrap_or_default();

            // Extract case cataloging
            let case_id_str = payload.get("case_id").map(|v| v.to_string()).unwrap_or_default();
            let case_subject_str = payload.get("case_subject").map(|v| v.to_string()).unwrap_or_default();
            let case_participants_str = payload.get("case_participants").map(|v| v.to_string()).unwrap_or_default();

            // Extract country identification
            let country_uuid_str = payload.get("country_uuid").map(|v| v.to_string()).unwrap_or_default();

            let message = Message {
                id: Uuid::parse_str(&id_str.trim_matches('"'))?,
                subject: Some(subject_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                from_address: Some(from_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                to_addresses: safe_parse_json_array(&to_addresses_str)?,
                sent_date: Some(sent_date_str.trim_matches('"'))
                    .filter(|s| !s.is_empty())
                    .and_then(|s| DateTime::parse_from_rfc3339(&s).ok())
                    .map(|dt| dt.with_timezone(&Utc)),
                plain_text_body_raw: Some(plain_text_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                html_body_raw: Some(html_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                cleaned_plain_text_body: Some(cleaned_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                file_path: Some(file_path_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                created_at: DateTime::parse_from_rfc3339(&created_at_str.trim_matches('"'))?.with_timezone(&Utc),
                updated_at: DateTime::parse_from_rfc3339(&updated_at_str.trim_matches('"'))?.with_timezone(&Utc),

                // Threading metadata
                thread_id: Some(thread_id_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                conversation_id: Some(conversation_id_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                thread_position: thread_position_str.trim_matches('"').parse::<u32>().ok(),
                email_weight: email_weight_str.trim_matches('"').parse::<f32>().ok(),
                email_type: Some(email_type_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                is_duplicate: is_duplicate_str.trim_matches('"').parse::<bool>().ok(),

                // Message headers
                message_id: Some(message_id_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                in_reply_to: safe_parse_json_array(&in_reply_to_str).unwrap_or_default(),
                references: safe_parse_json_array(&references_str).unwrap_or_default(),

                // Content analysis
                content_hash: Some(content_hash_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                normalized_subject: Some(normalized_subject_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),

                // Case cataloging
                case_id: Some(case_id_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                case_subject: Some(case_subject_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
                case_participants: safe_parse_json_array(&case_participants_str).unwrap_or_default(),

                // Country identification
                country_uuid: Some(country_uuid_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
            };

            // Collect all messages for now
            all_messages.push(message);
        }

        // Check if we should continue fetching
        if all_messages.len() >= fetch_limit as usize {
            break; // We have enough messages for processing
        }

        // Check if there are more results
        if let Some(next_offset) = scroll_result.next_page_offset {
            offset = Some(next_offset);
        } else {
            break; // No more results
        }
    }

    // Post-process the messages when no specific limit is provided
    if limit.is_none() {
        // Sort by date (newest first) and return the most recent emails
        all_messages.sort_by(|a, b| {
            let date_a = a.sent_date.unwrap_or(a.created_at);
            let date_b = b.sent_date.unwrap_or(b.created_at);
            date_b.cmp(&date_a) // Newest first
        });

        // Return the most recent emails (no filtering for now)
        all_messages.truncate(target_limit as usize);
        Ok(all_messages)
    } else {
        // For specific limits, return as-is (original behavior)
        all_messages.truncate(target_limit as usize);
        Ok(all_messages)
    }
}

/// Processes a single email file, generates embedding, and stores in Qdrant.
pub async fn ingest_email_file_with_embeddings(
    client: &Qdrant,
    file_path: &str,
    embedding_service_url: &str
) -> Result<Uuid, Box<dyn std::error::Error + Send + Sync>> {
    let parsed_email = if file_path.ends_with(".mbox") {
        // For mbox files, take the first email for now
        let emails = parse_mbox(file_path)?;
        if emails.is_empty() {
            return Err("No emails found in mbox file".into());
        }
        emails.into_iter().next().unwrap()
    } else {
        parse_eml(file_path)?
    };

    let mut message = Message::from(parsed_email);
    message.file_path = Some(file_path.to_string());

    // Generate embedding for the cleaned text
    if let Some(cleaned_text) = &message.cleaned_plain_text_body {
        if !cleaned_text.trim().is_empty() {
            let embedding = get_embedding(cleaned_text, embedding_service_url).await?;
            let message_id = insert_message_with_embedding(client, &message, embedding).await?;
            println!("✓ Ingested email with embedding: {} (ID: {})", file_path, message_id);
            Ok(message_id)
        } else {
            Err("No cleaned text available for embedding".into())
        }
    } else {
        Err("No cleaned text available for embedding".into())
    }
}

/// Processes all emails from an mbox file with embeddings.
pub async fn ingest_mbox_file_with_embeddings(
    client: &Qdrant,
    file_path: &str,
    embedding_service_url: &str
) -> Result<Vec<Uuid>, Box<dyn std::error::Error + Send + Sync>> {
    let emails = parse_mbox(file_path)?;
    let mut ingested_ids = Vec::new();

    for parsed_email in emails {
        let mut message = Message::from(parsed_email);
        message.file_path = Some(file_path.to_string());

        if let Some(cleaned_text) = &message.cleaned_plain_text_body {
            if !cleaned_text.trim().is_empty() {
                match get_embedding(cleaned_text, embedding_service_url).await {
                    Ok(embedding) => {
                        match insert_message_with_embedding(client, &message, embedding).await {
                            Ok(id) => {
                                ingested_ids.push(id);
                                println!("✓ Ingested email from mbox: {}", id);
                            }
                            Err(e) => eprintln!("Warning: Failed to insert email: {}", e),
                        }
                    }
                    Err(e) => eprintln!("Warning: Failed to generate embedding: {}", e),
                }
            } else {
                println!("⚠ Skipping email with empty text");
            }
        } else {
            println!("⚠ Skipping email with no cleaned text");
        }
    }

    println!("✓ Ingested {} emails from mbox: {}", ingested_ids.len(), file_path);
    Ok(ingested_ids)
}

/// Processes all emails from an enhanced mbox file with threading metadata and weighted embeddings.
pub async fn ingest_enhanced_mbox_file_with_embeddings(
    client: &Qdrant,
    file_path: &str,
    embedding_service_url: &str
) -> Result<Vec<Uuid>, Box<dyn std::error::Error + Send + Sync>> {
    println!("Processing enhanced mbox file with threading: {}", file_path);

    // Parse emails from mbox file
    let parsed_emails = parse_mbox(file_path)?;
    println!("Parsed {} emails from mbox file", parsed_emails.len());

    // Convert to ThreadedEmail format with header extraction
    let mut threaded_emails = process_emails_with_threading(parsed_emails, file_path).await?;

    // Apply email threading and case cataloguing
    let mut threader = EmailThreader::new();

    // Process each email for threading
    for email in &mut threaded_emails {
        // Generate content hash for duplicate detection
        if let Some(content) = &email.cleaned_plain_text_body {
            use sha2::{Sha256, Digest};
            let mut hasher = Sha256::new();
            hasher.update(content.as_bytes());
            email.content_hash = Some(format!("{:x}", hasher.finalize()));
        }

        // Generate normalized subject
        if let Some(subject) = &email.subject {
            email.normalized_subject = Some(normalize_subject(subject));
        }

        // Apply threading based on extracted headers
        if let Err(e) = threader.thread_email(email) {
            eprintln!("Warning: Threading failed for email {}: {}", email.id, e);
        }

        // Apply case cataloguing
        if let Err(e) = threader.catalog_email(email) {
            eprintln!("Warning: Case cataloguing failed for email {}: {}", email.id, e);
        }

        // Set email weight based on type
        email.weight = email.email_type.default_weight();
    }

    // Apply country identification to all emails
    for email in &mut threaded_emails {
        if let Some(country_code) = identify_country(email) {
            let country_uuid = generate_country_uuid(&country_code);
            // Store country information in the email for later use
            email.processing_notes.push(format!("Country identified: {} (UUID: {})", country_code, country_uuid));
        }
    }

    println!("Created {} conversation threads", threader.threads.len());
    println!("Created {} email cases", threader.cases.len());

    // Convert threaded emails to Message format and ingest
    let mut ingested_ids = Vec::new();

    for threaded_email in threaded_emails {
        let message = Message::from(threaded_email.clone());

        if let Some(cleaned_text) = &message.cleaned_plain_text_body {
            if !cleaned_text.trim().is_empty() {
                match get_embedding(cleaned_text, embedding_service_url).await {
                    Ok(embedding) => {
                        // Apply weighting to the embedding based on email type
                        let weight = threaded_email.weight;
                        let weighted_embedding: Vec<f32> = embedding.iter()
                            .map(|&x| x * weight)
                            .collect();

                        match insert_message_with_embedding(client, &message, weighted_embedding).await {
                            Ok(id) => {
                                ingested_ids.push(id);
                                println!("✓ Ingested threaded email: {} (type: {:?}, weight: {:.2})",
                                    id, threaded_email.email_type, weight);
                            }
                            Err(e) => eprintln!("Warning: Failed to insert email: {}", e),
                        }
                    }
                    Err(e) => eprintln!("Warning: Failed to generate embedding: {}", e),
                }
            } else {
                println!("⚠ Skipping email with empty text");
            }
        } else {
            println!("⚠ Skipping email with no cleaned text");
        }
    }

    println!("✓ Ingested {} threaded emails from mbox: {}", ingested_ids.len(), file_path);
    Ok(ingested_ids)
}

/// Searches for similar messages with thread-aware prioritization
pub async fn search_similar_messages_with_thread_priority(
    client: &Qdrant,
    query_embedding: Vec<f32>,
    limit: u64,
    thread_id: Option<String>,
    conversation_id: Option<String>,
    collection_name: &str
) -> Result<Vec<Message>, Box<dyn std::error::Error + Send + Sync>> {
    use qdrant_client::qdrant::{Filter, Condition, FieldCondition, Match};

    let mut search_request = SearchPoints {
        collection_name: collection_name.to_string(),
        vector: query_embedding,
        limit,
        with_payload: Some(WithPayloadSelector {
            selector_options: Some(qdrant_client::qdrant::with_payload_selector::SelectorOptions::Enable(true)),
        }),
        ..Default::default()
    };

    // If thread context is provided, create a filter to prioritize thread emails
    if let Some(tid) = thread_id {
        let thread_condition = Condition {
            condition_one_of: Some(qdrant_client::qdrant::condition::ConditionOneOf::Field(
                FieldCondition {
                    key: "thread_id".to_string(),
                    r#match: Some(Match {
                        match_value: Some(qdrant_client::qdrant::r#match::MatchValue::Keyword(tid)),
                    }),
                    ..Default::default()
                }
            )),
        };

        search_request.filter = Some(Filter {
            should: vec![thread_condition],
            ..Default::default()
        });
    } else if let Some(cid) = conversation_id {
        let conversation_condition = Condition {
            condition_one_of: Some(qdrant_client::qdrant::condition::ConditionOneOf::Field(
                FieldCondition {
                    key: "conversation_id".to_string(),
                    r#match: Some(Match {
                        match_value: Some(qdrant_client::qdrant::r#match::MatchValue::Keyword(cid)),
                    }),
                    ..Default::default()
                }
            )),
        };

        search_request.filter = Some(Filter {
            should: vec![conversation_condition],
            ..Default::default()
        });
    }

    let search_result = client.search_points(search_request).await?;
    let mut messages = Vec::new();

    for scored_point in search_result.result {
        let message = reconstruct_message_from_payload(scored_point.payload)?;
        messages.push(message);
    }

    Ok(messages)
}

/// Retrieves all emails in a conversation thread
pub async fn get_conversation_thread(
    client: &Qdrant,
    thread_id: String,
    collection_name: &str
) -> Result<Vec<Message>, Box<dyn std::error::Error + Send + Sync>> {
    use qdrant_client::qdrant::{Filter, Condition, FieldCondition, Match};

    let thread_condition = Condition {
        condition_one_of: Some(qdrant_client::qdrant::condition::ConditionOneOf::Field(
            FieldCondition {
                key: "thread_id".to_string(),
                r#match: Some(Match {
                    match_value: Some(qdrant_client::qdrant::r#match::MatchValue::Keyword(thread_id)),
                }),
                ..Default::default()
            }
        )),
    };

    let scroll_request = ScrollPoints {
        collection_name: collection_name.to_string(),
        filter: Some(Filter {
            must: vec![thread_condition],
            ..Default::default()
        }),
        limit: Some(100), // Reasonable limit for conversation threads
        with_payload: Some(WithPayloadSelector {
            selector_options: Some(qdrant_client::qdrant::with_payload_selector::SelectorOptions::Enable(true)),
        }),
        ..Default::default()
    };

    let scroll_result = client.scroll(scroll_request).await?;
    let mut messages = Vec::new();

    for point in scroll_result.result {
        let message = reconstruct_message_from_payload(point.payload)?;
        messages.push(message);
    }

    // Sort by thread position and sent date
    messages.sort_by(|a, b| {
        match (a.thread_position, b.thread_position) {
            (Some(pos_a), Some(pos_b)) => pos_a.cmp(&pos_b),
            (Some(_), None) => std::cmp::Ordering::Less,
            (None, Some(_)) => std::cmp::Ordering::Greater,
            (None, None) => {
                match (&a.sent_date, &b.sent_date) {
                    (Some(date_a), Some(date_b)) => date_a.cmp(date_b),
                    (Some(_), None) => std::cmp::Ordering::Less,
                    (None, Some(_)) => std::cmp::Ordering::Greater,
                    (None, None) => std::cmp::Ordering::Equal,
                }
            }
        }
    });

    Ok(messages)
}

/// Searches for emails within a specific case
pub async fn search_emails_by_case(
    client: &Qdrant,
    case_id: String,
    collection_name: &str
) -> Result<Vec<Message>, Box<dyn std::error::Error + Send + Sync>> {
    use qdrant_client::qdrant::{Filter, Condition, FieldCondition, Match};

    let case_condition = Condition {
        condition_one_of: Some(qdrant_client::qdrant::condition::ConditionOneOf::Field(
            FieldCondition {
                key: "case_id".to_string(),
                r#match: Some(Match {
                    match_value: Some(qdrant_client::qdrant::r#match::MatchValue::Keyword(case_id)),
                }),
                ..Default::default()
            }
        )),
    };

    let scroll_request = ScrollPoints {
        collection_name: collection_name.to_string(),
        filter: Some(Filter {
            must: vec![case_condition],
            ..Default::default()
        }),
        limit: Some(1000), // Higher limit for case emails
        with_payload: Some(WithPayloadSelector {
            selector_options: Some(qdrant_client::qdrant::with_payload_selector::SelectorOptions::Enable(true)),
        }),
        ..Default::default()
    };

    let scroll_result = client.scroll(scroll_request).await?;
    let mut messages = Vec::new();

    for point in scroll_result.result {
        let message = reconstruct_message_from_payload(point.payload)?;
        messages.push(message);
    }

    // Sort by sent date (newest first)
    messages.sort_by(|a, b| {
        match (&b.sent_date, &a.sent_date) {
            (Some(date_b), Some(date_a)) => date_b.cmp(date_a),
            (Some(_), None) => std::cmp::Ordering::Less,
            (None, Some(_)) => std::cmp::Ordering::Greater,
            (None, None) => std::cmp::Ordering::Equal,
        }
    });

    Ok(messages)
}

/// Helper function to reconstruct Message from Qdrant payload
fn reconstruct_message_from_payload(
    payload: std::collections::HashMap<String, Value>
) -> Result<Message, Box<dyn std::error::Error + Send + Sync>> {
    // This function reuses the same logic as in search_similar_messages and get_recent_messages
    // Extract basic fields
    let id_str = payload.get("id").map(|v| v.to_string()).unwrap_or_default();
    let subject_str = payload.get("subject").map(|v| v.to_string()).unwrap_or_default();
    let from_str = payload.get("from_address").map(|v| v.to_string()).unwrap_or_default();
    let to_addresses_str = payload.get("to_addresses").map(|v| v.to_string()).unwrap_or_default();
    let sent_date_str = payload.get("sent_date").map(|v| v.to_string()).unwrap_or_default();
    let plain_text_str = payload.get("plain_text_body_raw").map(|v| v.to_string()).unwrap_or_default();
    let html_str = payload.get("html_body_raw").map(|v| v.to_string()).unwrap_or_default();
    let cleaned_str = payload.get("cleaned_plain_text_body").map(|v| v.to_string()).unwrap_or_default();
    let file_path_str = payload.get("file_path").map(|v| v.to_string()).unwrap_or_default();
    let created_at_str = payload.get("created_at").map(|v| v.to_string()).unwrap_or_default();
    let updated_at_str = payload.get("updated_at").map(|v| v.to_string()).unwrap_or_default();

    // Extract threading metadata with safe defaults
    let thread_id_str = payload.get("thread_id").map(|v| v.to_string()).unwrap_or_default();
    let conversation_id_str = payload.get("conversation_id").map(|v| v.to_string()).unwrap_or_default();
    let thread_position_str = payload.get("thread_position").map(|v| v.to_string()).unwrap_or_default();
    let email_weight_str = payload.get("email_weight").map(|v| v.to_string()).unwrap_or_default();
    let email_type_str = payload.get("email_type").map(|v| v.to_string()).unwrap_or_default();
    let is_duplicate_str = payload.get("is_duplicate").map(|v| v.to_string()).unwrap_or_default();

    // Extract message headers
    let message_id_str = payload.get("message_id").map(|v| v.to_string()).unwrap_or_default();
    let in_reply_to_str = payload.get("in_reply_to").map(|v| v.to_string()).unwrap_or_default();
    let references_str = payload.get("references").map(|v| v.to_string()).unwrap_or_default();

    // Extract content analysis
    let content_hash_str = payload.get("content_hash").map(|v| v.to_string()).unwrap_or_default();
    let normalized_subject_str = payload.get("normalized_subject").map(|v| v.to_string()).unwrap_or_default();

    // Extract case cataloging
    let case_id_str = payload.get("case_id").map(|v| v.to_string()).unwrap_or_default();
    let case_subject_str = payload.get("case_subject").map(|v| v.to_string()).unwrap_or_default();
    let case_participants_str = payload.get("case_participants").map(|v| v.to_string()).unwrap_or_default();

    // Extract country identification
    let country_uuid_str = payload.get("country_uuid").map(|v| v.to_string()).unwrap_or_default();

    let message = Message {
        id: Uuid::parse_str(&id_str.trim_matches('"'))?,
        subject: Some(subject_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
        from_address: Some(from_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
        to_addresses: safe_parse_json_array(&to_addresses_str)?,
        sent_date: Some(sent_date_str.trim_matches('"'))
            .filter(|s| !s.is_empty())
            .and_then(|s| DateTime::parse_from_rfc3339(&s).ok())
            .map(|dt| dt.with_timezone(&Utc)),
        plain_text_body_raw: Some(plain_text_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
        html_body_raw: Some(html_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
        cleaned_plain_text_body: Some(cleaned_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
        file_path: Some(file_path_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
        created_at: DateTime::parse_from_rfc3339(&created_at_str.trim_matches('"'))?.with_timezone(&Utc),
        updated_at: DateTime::parse_from_rfc3339(&updated_at_str.trim_matches('"'))?.with_timezone(&Utc),

        // Threading metadata
        thread_id: Some(thread_id_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
        conversation_id: Some(conversation_id_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
        thread_position: thread_position_str.trim_matches('"').parse::<u32>().ok(),
        email_weight: email_weight_str.trim_matches('"').parse::<f32>().ok(),
        email_type: Some(email_type_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
        is_duplicate: is_duplicate_str.trim_matches('"').parse::<bool>().ok(),

        // Message headers
        message_id: Some(message_id_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
        in_reply_to: safe_parse_json_array(&in_reply_to_str).unwrap_or_default(),
        references: safe_parse_json_array(&references_str).unwrap_or_default(),

        // Content analysis
        content_hash: Some(content_hash_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
        normalized_subject: Some(normalized_subject_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),

        // Case cataloging
        case_id: Some(case_id_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
        case_subject: Some(case_subject_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
        case_participants: safe_parse_json_array(&case_participants_str).unwrap_or_default(),

        // Country identification
        country_uuid: Some(country_uuid_str.trim_matches('"').to_string()).filter(|s| !s.is_empty()),
    };

    Ok(message)
}

// Embedding service integration

#[derive(Debug, Serialize)]
struct EmbeddingRequest {
    text: String,
    normalize: Option<bool>,
}

#[derive(Debug, Deserialize)]
struct EmbeddingResponse {
    embedding: Vec<f32>,
    #[allow(dead_code)]
    model_name: String,
    #[allow(dead_code)]
    dimension: usize,
}

/// Detects if a file is a Thunderbird mbox file (no extension, contains email headers) or a Thunderbird .txt export
pub fn is_thunderbird_mbox(file_path: &str) -> bool {
    use std::path::Path;

    let path = Path::new(file_path);

    // Check if file has no extension OR has .mbox extension (for cleaned files) OR has .txt extension (for Thunderbird exports)
    if let Some(ext) = path.extension() {
        if ext != "mbox" && ext != "txt" {
            return false;
        }

        // For .txt files, check if filename suggests Thunderbird export (Inbox*, Sent*, etc.)
        if ext == "txt" {
            return is_thunderbird_txt_export(path);
        }
    }

    // Check if it's in a .sbd directory or has typical Thunderbird names
    let parent_dir = path.parent().map(|p| p.to_string_lossy().to_lowercase());
    let file_name = path.file_name().map(|n| n.to_string_lossy().to_lowercase());

    if let Some(parent) = parent_dir {
        if parent.contains(".sbd") {
            return true;
        }
    }

    // Check for common Thunderbird folder names
    if let Some(name) = file_name {
        if ["inbox", "sent", "drafts", "trash", "junk", "outbox", "templates"].contains(&name.as_str()) {
            return true;
        }
    }

    // Try to read first few lines to detect mbox format (handle binary data)
    if let Ok(bytes) = std::fs::read(file_path) {
        // Try to convert first 2KB to string, ignoring invalid UTF-8
        let preview_size = std::cmp::min(2048, bytes.len());
        let preview = String::from_utf8_lossy(&bytes[..preview_size]);
        let lines: Vec<&str> = preview.lines().take(10).collect();
        for line in lines {
            // Look for typical mbox "From " separator or Thunderbird "From - " separator
            if (line.starts_with("From ") && line.contains("@")) ||
               line.starts_with("From - ") {
                return true;
            }
            // Look for Mozilla/Thunderbird specific headers
            if line.starts_with("X-Mozilla-Status:") ||
               line.starts_with("X-Mozilla-Status2:") {
                return true;
            }
            // Look for standard email headers
            if line.starts_with("Return-Path:") ||
               line.starts_with("Delivered-To:") ||
               line.starts_with("Received:") ||
               line.starts_with("Message-ID:") {
                return true;
            }
        }
    }

    false
}

/// Detects if a .txt file is a Thunderbird email export based on filename patterns
fn is_thunderbird_txt_export(path: &std::path::Path) -> bool {
    let file_name = path.file_name()
        .and_then(|n| n.to_str())
        .unwrap_or("")
        .to_lowercase();

    // Check for common Thunderbird export patterns
    file_name.starts_with("inbox") ||
    file_name.starts_with("sent") ||
    file_name.starts_with("drafts") ||
    file_name.starts_with("trash") ||
    file_name.starts_with("junk")
}

/// Extract folder type from .txt filename (e.g., "Inbox2024" -> "Inbox")
pub fn extract_folder_type_from_txt_filename(file_path: &str) -> String {
    use std::path::Path;

    let path = Path::new(file_path);
    let file_name = path.file_stem()
        .and_then(|n| n.to_str())
        .unwrap_or("")
        .to_lowercase();

    // Extract base folder name by removing year/date suffixes
    if file_name.starts_with("inbox") {
        "Inbox".to_string()
    } else if file_name.starts_with("sent") {
        "Sent".to_string()
    } else if file_name.starts_with("drafts") {
        "Drafts".to_string()
    } else if file_name.starts_with("trash") {
        "Trash".to_string()
    } else if file_name.starts_with("junk") {
        "Junk".to_string()
    } else {
        // Fallback: capitalize first letter
        let mut chars = file_name.chars();
        match chars.next() {
            None => "Unknown".to_string(),
            Some(first) => first.to_uppercase().collect::<String>() + chars.as_str(),
        }
    }
}

/// Helper function to safely parse JSON from Qdrant payload
fn safe_parse_json_array(json_str: &str) -> Result<Vec<String>, Box<dyn std::error::Error + Send + Sync>> {
    let trimmed = json_str.trim_matches('"');

    // Try parsing directly first
    if let Ok(result) = serde_json::from_str::<Vec<String>>(trimmed) {
        return Ok(result);
    }

    // If that fails, try parsing as a string and then parsing the inner content
    if let Ok(inner_str) = serde_json::from_str::<String>(trimmed) {
        if let Ok(result) = serde_json::from_str::<Vec<String>>(&inner_str) {
            return Ok(result);
        }
    }

    // Fallback: return empty array
    Ok(vec![])
}

/// Generates an embedding for the given text using the embedding service.
pub async fn get_embedding(text: &str, embedding_service_url: &str) -> Result<Vec<f32>, Box<dyn std::error::Error + Send + Sync>> {
    let client = Client::new();

    let request = EmbeddingRequest {
        text: text.to_string(),
        normalize: Some(true),
    };

    let response = client
        .post(&format!("{}/embed", embedding_service_url))
        .json(&request)
        .send()
        .await?;

    if response.status().is_success() {
        let embedding_response: EmbeddingResponse = response.json().await?;
        Ok(embedding_response.embedding)
    } else {
        let error_text = response.text().await?;
        Err(format!("Embedding service error: {}", error_text).into())
    }
}

/// Extracts threading headers from raw email content
pub fn extract_threading_headers(raw_content: &str) -> (Option<String>, Vec<String>, Vec<String>) {
    let mut message_id = None;
    let mut in_reply_to = Vec::new();
    let mut references = Vec::new();

    // Parse headers line by line
    for line in raw_content.lines() {
        let line = line.trim();

        // Stop at empty line (end of headers)
        if line.is_empty() {
            break;
        }

        // Extract Message-ID
        if line.to_lowercase().starts_with("message-id:") {
            if let Some(id) = line.split(':').nth(1) {
                message_id = Some(id.trim().to_string());
            }
        }

        // Extract In-Reply-To
        if line.to_lowercase().starts_with("in-reply-to:") {
            if let Some(reply_to) = line.split(':').nth(1) {
                // Parse multiple message IDs separated by whitespace
                for id in reply_to.split_whitespace() {
                    if !id.trim().is_empty() {
                        in_reply_to.push(id.trim().to_string());
                    }
                }
            }
        }

        // Extract References
        if line.to_lowercase().starts_with("references:") {
            if let Some(refs) = line.split(':').nth(1) {
                // Parse multiple message IDs separated by whitespace
                for id in refs.split_whitespace() {
                    if !id.trim().is_empty() {
                        references.push(id.trim().to_string());
                    }
                }
            }
        }
    }

    (message_id, in_reply_to, references)
}

/// Enhanced function to process emails with threading and header extraction
pub async fn process_emails_with_threading(
    emails: Vec<ParsedEmail>,
    file_path: &str
) -> Result<Vec<ThreadedEmail>, Box<dyn std::error::Error + Send + Sync>> {
    let mut threaded_emails = Vec::new();

    for parsed_email in emails {
        let mut threaded_email = ThreadedEmail::from(parsed_email.clone());

        // Extract threading headers from raw content if available
        if let Some(raw_content) = &parsed_email.plain_text_body_raw {
            let (message_id, in_reply_to, references) = extract_threading_headers(raw_content);
            threaded_email.message_id = message_id;
            threaded_email.in_reply_to = in_reply_to;
            threaded_email.references = references;
        }

        // Set folder path
        threaded_email.folder_path = Some(file_path.to_string());

        // Determine email type from folder path
        threaded_email.email_type = if file_path.to_lowercase().contains("sent") {
            EmailType::Sent
        } else if file_path.to_lowercase().contains("draft") {
            EmailType::Draft
        } else {
            EmailType::Received
        };

        threaded_emails.push(threaded_email);
    }

    Ok(threaded_emails)
}

/// Normalizes email subject by removing common prefixes and cleaning up
pub fn normalize_subject(subject: &str) -> String {
    let mut normalized = subject.to_lowercase();

    // Remove common prefixes
    let prefixes = ["re:", "fwd:", "fw:", "[external]", "re[2]:", "re[3]:", "re[4]:", "re[5]:"];
    for prefix in &prefixes {
        if normalized.starts_with(prefix) {
            normalized = normalized[prefix.len()..].trim().to_string();
        }
    }

    // Remove extra whitespace
    normalized = normalized.split_whitespace().collect::<Vec<_>>().join(" ");

    // Remove common email artifacts
    normalized = normalized.replace("automatic reply:", "");
    normalized = normalized.replace("out of office:", "");

    normalized.trim().to_string()
}

/// Identifies the country associated with an email based on various indicators
pub fn identify_country(email: &ThreadedEmail) -> Option<String> {
    // Country identification based on email domains, content, and participants
    let mut country_indicators = Vec::new();

    // Check email domains for country indicators
    if let Some(from) = &email.from {
        if let Some(domain) = from.split('@').nth(1) {
            let country = match domain.to_lowercase().as_str() {
                d if d.ends_with(".uk") || d.ends_with(".co.uk") => Some("GB"),
                d if d.ends_with(".de") => Some("DE"),
                d if d.ends_with(".fr") => Some("FR"),
                d if d.ends_with(".it") => Some("IT"),
                d if d.ends_with(".es") => Some("ES"),
                d if d.ends_with(".nl") => Some("NL"),
                d if d.ends_with(".be") => Some("BE"),
                d if d.ends_with(".ch") => Some("CH"),
                d if d.ends_with(".at") => Some("AT"),
                d if d.ends_with(".se") => Some("SE"),
                d if d.ends_with(".no") => Some("NO"),
                d if d.ends_with(".dk") => Some("DK"),
                d if d.ends_with(".fi") => Some("FI"),
                d if d.ends_with(".pl") => Some("PL"),
                d if d.ends_with(".cz") => Some("CZ"),
                d if d.ends_with(".hu") => Some("HU"),
                d if d.ends_with(".ca") => Some("CA"),
                d if d.ends_with(".au") => Some("AU"),
                d if d.ends_with(".nz") => Some("NZ"),
                d if d.ends_with(".jp") => Some("JP"),
                d if d.ends_with(".kr") => Some("KR"),
                d if d.ends_with(".cn") => Some("CN"),
                d if d.ends_with(".in") => Some("IN"),
                d if d.ends_with(".br") => Some("BR"),
                d if d.ends_with(".mx") => Some("MX"),
                d if d.ends_with(".ar") => Some("AR"),
                d if d.ends_with(".cl") => Some("CL"),
                d if d.ends_with(".za") => Some("ZA"),
                d if d.ends_with(".sg") => Some("SG"),
                d if d.ends_with(".hk") => Some("HK"),
                d if d.ends_with(".tw") => Some("TW"),
                d if d.ends_with(".th") => Some("TH"),
                d if d.ends_with(".my") => Some("MY"),
                d if d.ends_with(".id") => Some("ID"),
                d if d.ends_with(".ph") => Some("PH"),
                d if d.ends_with(".vn") => Some("VN"),
                d if d.ends_with(".ru") => Some("RU"),
                d if d.ends_with(".ua") => Some("UA"),
                d if d.ends_with(".tr") => Some("TR"),
                d if d.ends_with(".il") => Some("IL"),
                d if d.ends_with(".ae") => Some("AE"),
                d if d.ends_with(".sa") => Some("SA"),
                d if d.ends_with(".eg") => Some("EG"),
                _ => {
                    // Default to US for .com, .org, .net, .edu, .gov domains
                    if domain.ends_with(".com") || domain.ends_with(".org") || domain.ends_with(".net") ||
                       domain.ends_with(".edu") || domain.ends_with(".gov") {
                        Some("US")
                    } else {
                        None
                    }
                }
            };
            if let Some(c) = country {
                country_indicators.push(c.to_string());
            }
        }
    }

    // Check recipient domains
    for to_addr in &email.to {
        if let Some(domain) = to_addr.split('@').nth(1) {
            if domain.to_lowercase().ends_with(".uk") || domain.to_lowercase().ends_with(".co.uk") {
                country_indicators.push("GB".to_string());
            } else if domain.to_lowercase().ends_with(".de") {
                country_indicators.push("DE".to_string());
            } else if domain.to_lowercase().ends_with(".fr") {
                country_indicators.push("FR".to_string());
            }
            // Add more domain checks as needed
        }
    }

    // Check email content for country/location keywords
    if let Some(content) = &email.cleaned_plain_text_body {
        let content_lower = content.to_lowercase();

        // European countries
        if content_lower.contains("united kingdom") || content_lower.contains("uk ") ||
           content_lower.contains("london") || content_lower.contains("manchester") ||
           content_lower.contains("birmingham") || content_lower.contains("glasgow") {
            country_indicators.push("GB".to_string());
        }
        if content_lower.contains("germany") || content_lower.contains("deutschland") ||
           content_lower.contains("berlin") || content_lower.contains("munich") ||
           content_lower.contains("hamburg") || content_lower.contains("frankfurt") {
            country_indicators.push("DE".to_string());
        }
        if content_lower.contains("france") || content_lower.contains("paris") ||
           content_lower.contains("lyon") || content_lower.contains("marseille") {
            country_indicators.push("FR".to_string());
        }

        // North America
        if content_lower.contains("united states") || content_lower.contains("usa") ||
           content_lower.contains("new york") || content_lower.contains("california") ||
           content_lower.contains("texas") || content_lower.contains("florida") {
            country_indicators.push("US".to_string());
        }
        if content_lower.contains("canada") || content_lower.contains("toronto") ||
           content_lower.contains("vancouver") || content_lower.contains("montreal") {
            country_indicators.push("CA".to_string());
        }

        // Add more content-based detection as needed
    }

    // Return the most common country indicator, or None if no clear indication
    if country_indicators.is_empty() {
        None
    } else {
        // Count occurrences and return the most frequent
        let mut counts = std::collections::HashMap::new();
        for country in country_indicators {
            *counts.entry(country).or_insert(0) += 1;
        }
        counts.into_iter().max_by_key(|(_, count)| *count).map(|(country, _)| country)
    }
}

/// Generates a country UUID based on the country code for consistent case organization
pub fn generate_country_uuid(country_code: &str) -> String {
    use sha2::{Sha256, Digest};
    let mut hasher = Sha256::new();
    hasher.update(format!("country:{}", country_code).as_bytes());
    let hash = hasher.finalize();

    // Create a UUID-like string from the hash
    let hash_str = format!("{:x}", hash);
    format!("{}-{}-{}-{}-{}",
        &hash_str[0..8],
        &hash_str[8..12],
        &hash_str[12..16],
        &hash_str[16..20],
        &hash_str[20..32]
    )
}

#[cfg(test)]
mod tests {
    use super::*;
    use std::io::Write;
    use tempfile::NamedTempFile;

    #[test]
    fn test_html_to_plain_text() {
        let html_content = "<html><body><h1>Hello</h1><p>This is <b>HTML</b> with a <a href=\"http://example.com\">link</a>.</p><ul><li>Item 1</li><li>Item 2</li></ul></body></html>";
        let plain_text = html_to_plain_text(html_content);
        assert!(plain_text.contains("Hello"));
        assert!(plain_text.contains("HTML"));
        assert!(plain_text.contains("Item 1"));
        assert!(plain_text.contains("Item 2"));
    }

    #[test]
    fn test_strip_signatures_and_quotes() {
        let text_with_signature = "Hello,\n\nThis is the main body.\n\n-- \nJohn Doe\nSoftware Engineer";
        let cleaned = strip_signatures_and_quotes(text_with_signature);
        assert_eq!(cleaned, "Hello,\n\nThis is the main body.");

        let text_with_quote = "Hello,\n\nMy reply.\n\n> On Mon, Jan 1, 2023 at 10:00 AM John Doe <<EMAIL>> wrote:\n> This is a quoted message.";
        let cleaned_quote = strip_signatures_and_quotes(text_with_quote);
        assert_eq!(cleaned_quote, "Hello,\n\nMy reply.");
    }

    #[test]
    fn test_parse_valid_eml() -> Result<(), Box<dyn std::error::Error>> {
        let eml_content = "From: <EMAIL>\nSubject: Test Subject\nTo: <EMAIL>\n\nHello, this is a test email.";
        let mut file = NamedTempFile::new()?;
        file.write_all(eml_content.as_bytes())?;
        let file_path = file.path().to_str().unwrap();

        let parsed_email = parse_eml(file_path)?;
        assert_eq!(parsed_email.subject, Some("Test Subject".to_string()));
        assert_eq!(parsed_email.from, Some("<EMAIL>".to_string()));
        assert_eq!(parsed_email.to, vec!["<EMAIL>".to_string()]);
        assert!(parsed_email.cleaned_plain_text_body.is_some());
        Ok(())
    }

    #[test]
    fn test_parse_mbox_single_email() -> Result<(), Box<dyn std::error::Error>> {
        let mbox_content = "From <EMAIL> Mon Jan  1 10:00:00 2024\nFrom: <EMAIL>\nSubject: Test Subject\nTo: <EMAIL>\n\nHello, this is a test email.\n\n";
        let mut file = NamedTempFile::new()?;
        file.write_all(mbox_content.as_bytes())?;
        let file_path = file.path().to_str().unwrap();

        let parsed_emails = parse_mbox(file_path)?;
        assert_eq!(parsed_emails.len(), 1);

        let email = &parsed_emails[0];
        assert_eq!(email.subject, Some("Test Subject".to_string()));
        assert_eq!(email.from, Some("<EMAIL>".to_string()));
        assert_eq!(email.to, vec!["<EMAIL>".to_string()]);
        assert!(email.cleaned_plain_text_body.is_some());
        Ok(())
    }

    #[test]
    fn test_parse_mbox_multiple_emails() -> Result<(), Box<dyn std::error::Error>> {
        let mbox_content = "From <EMAIL> Mon Jan  1 10:00:00 2024\nFrom: <EMAIL>\nSubject: First Email\nTo: <EMAIL>\n\nFirst email content.\n\nFrom <EMAIL> Mon Jan  1 11:00:00 2024\nFrom: <EMAIL>\nSubject: Second Email\nTo: <EMAIL>\n\nSecond email content.\n\n";
        let mut file = NamedTempFile::new()?;
        file.write_all(mbox_content.as_bytes())?;
        let file_path = file.path().to_str().unwrap();

        let parsed_emails = parse_mbox(file_path)?;
        assert_eq!(parsed_emails.len(), 2);

        assert_eq!(parsed_emails[0].subject, Some("First Email".to_string()));
        assert_eq!(parsed_emails[0].from, Some("<EMAIL>".to_string()));

        assert_eq!(parsed_emails[1].subject, Some("Second Email".to_string()));
        assert_eq!(parsed_emails[1].from, Some("<EMAIL>".to_string()));
        Ok(())
    }

    #[test]
    fn test_parse_eml_with_attachments() -> Result<(), Box<dyn std::error::Error>> {
        let eml_content = "From: <EMAIL>\nSubject: Email with Attachment\nTo: <EMAIL>\nContent-Type: multipart/mixed; boundary=\"boundary123\"\n\n--boundary123\nContent-Type: text/plain\n\nThis email has an attachment.\n\n--boundary123\nContent-Type: application/pdf\nContent-Disposition: attachment; filename=\"document.pdf\"\n\nPDF content here\n--boundary123--";
        let mut file = NamedTempFile::new()?;
        file.write_all(eml_content.as_bytes())?;
        let file_path = file.path().to_str().unwrap();

        let parsed_email = parse_eml(file_path)?;
        assert_eq!(parsed_email.subject, Some("Email with Attachment".to_string()));
        assert_eq!(parsed_email.from, Some("<EMAIL>".to_string()));
        assert!(parsed_email.cleaned_plain_text_body.is_some());
        Ok(())
    }

    #[test]
    fn test_parse_eml_html_content() -> Result<(), Box<dyn std::error::Error>> {
        let eml_content = "From: <EMAIL>\nSubject: HTML Email\nTo: <EMAIL>\nContent-Type: text/html\n\n<html><body><h1>Hello</h1><p>This is an HTML email.</p></body></html>";
        let mut file = NamedTempFile::new()?;
        file.write_all(eml_content.as_bytes())?;
        let file_path = file.path().to_str().unwrap();

        let parsed_email = parse_eml(file_path)?;
        assert_eq!(parsed_email.subject, Some("HTML Email".to_string()));
        assert_eq!(parsed_email.from, Some("<EMAIL>".to_string()));

        // Should extract plain text from HTML
        if let Some(body) = &parsed_email.cleaned_plain_text_body {
            assert!(body.contains("Hello"));
            assert!(body.contains("This is an HTML email"));
            assert!(!body.contains("<html>"));
            assert!(!body.contains("<body>"));
        }
        Ok(())
    }

    #[test]
    fn test_parse_eml_missing_headers() -> Result<(), Box<dyn std::error::Error>> {
        let eml_content = "Subject: Missing From Header\n\nThis email is missing the From header.";
        let mut file = NamedTempFile::new()?;
        file.write_all(eml_content.as_bytes())?;
        let file_path = file.path().to_str().unwrap();

        let parsed_email = parse_eml(file_path)?;
        assert_eq!(parsed_email.subject, Some("Missing From Header".to_string()));
        assert_eq!(parsed_email.from, None);
        assert!(parsed_email.cleaned_plain_text_body.is_some());
        Ok(())
    }

    #[test]
    fn test_parse_eml_unicode_content() -> Result<(), Box<dyn std::error::Error>> {
        let eml_content = "From: <EMAIL>\nSubject: Unicode Test 🚀\nTo: <EMAIL>\n\nHello! This email contains unicode: 你好 🌟 café";
        let mut file = NamedTempFile::new()?;
        file.write_all(eml_content.as_bytes())?;
        let file_path = file.path().to_str().unwrap();

        let parsed_email = parse_eml(file_path)?;
        assert_eq!(parsed_email.subject, Some("Unicode Test 🚀".to_string()));
        assert_eq!(parsed_email.from, Some("<EMAIL>".to_string()));

        if let Some(body) = &parsed_email.cleaned_plain_text_body {
            assert!(body.contains("你好"));
            assert!(body.contains("🌟"));
            assert!(body.contains("café"));
        }
        Ok(())
    }

    #[test]
    fn test_parse_eml_excludes_binary_attachments() -> Result<(), Box<dyn std::error::Error>> {
        // Create a multipart email with text content and binary attachment
        let eml_content = r#"From: <EMAIL>
Subject: Email with Binary Attachment
To: <EMAIL>
Content-Type: multipart/mixed; boundary="boundary123"

--boundary123
Content-Type: text/plain; charset="utf-8"

This is the actual email text content that should be extracted.

--boundary123
Content-Type: application/octet-stream
Content-Disposition: attachment; filename="binary_file.bin"
Content-Transfer-Encoding: base64

iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU8
--boundary123
Content-Type: image/jpeg
Content-Disposition: attachment; filename="image.jpg"

BINARY_IMAGE_DATA_HERE_SHOULD_BE_EXCLUDED
--boundary123--"#;

        let mut file = NamedTempFile::new()?;
        file.write_all(eml_content.as_bytes())?;
        let file_path = file.path().to_str().unwrap();

        let parsed_email = parse_eml(file_path)?;
        assert_eq!(parsed_email.subject, Some("Email with Binary Attachment".to_string()));
        assert_eq!(parsed_email.from, Some("<EMAIL>".to_string()));

        // Verify that only text content is extracted
        if let Some(body) = &parsed_email.cleaned_plain_text_body {
            assert!(body.contains("This is the actual email text content"));
            // Verify binary content is NOT included
            assert!(!body.contains("iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJ"));
            assert!(!body.contains("BINARY_IMAGE_DATA_HERE_SHOULD_BE_EXCLUDED"));
            assert!(!body.contains("base64"));
        } else {
            panic!("Expected cleaned_plain_text_body to contain text content");
        }
        Ok(())
    }

    #[test]
    fn test_parse_eml_greek_characters() -> Result<(), Box<dyn std::error::Error>> {
        // Create an email with Greek characters in various encodings
        let eml_content = r#"From: <EMAIL>
Subject: =?UTF-8?B?zp/Ouc66zr/Ovc6/zrzOuc66z4wgzrXOuc+CzrHOuc+EzrfPgc65zrE=?=
To: <EMAIL>
Content-Type: text/plain; charset="utf-8"
Content-Transfer-Encoding: 8bit

Γεια σας! Αυτό είναι ένα email με ελληνικούς χαρακτήρες.

Περιεχόμενο:
• Κείμενο στα ελληνικά
• Ειδικοί χαρακτήρες: άλφα, βήτα, γάμμα
• Τόνοι και διαλυτικά: ά, έ, ή, ί, ό, ύ, ώ, ΐ, ΰ

Τέλος μηνύματος."#;

        let mut file = NamedTempFile::new()?;
        file.write_all(eml_content.as_bytes())?;
        let file_path = file.path().to_str().unwrap();

        let parsed_email = parse_eml(file_path)?;

        // Check that the subject is properly decoded
        assert!(parsed_email.subject.is_some());
        let subject = parsed_email.subject.as_ref().unwrap();
        assert!(subject.contains("Οικονομικό"), "Subject should contain Greek text: {}", subject);

        assert_eq!(parsed_email.from, Some("<EMAIL>".to_string()));

        // Verify that Greek characters are preserved in the body
        if let Some(body) = &parsed_email.cleaned_plain_text_body {
            assert!(body.contains("Γεια σας"), "Body should contain 'Γεια σας': {}", body);
            assert!(body.contains("ελληνικούς χαρακτήρες"), "Body should contain 'ελληνικούς χαρακτήρες': {}", body);
            assert!(body.contains("άλφα, βήτα, γάμμα"), "Body should contain Greek letters: {}", body);
            assert!(body.contains("ά, έ, ή, ί, ό, ύ, ώ"), "Body should contain accented characters: {}", body);
            assert!(body.contains("Τέλος μηνύματος"), "Body should contain 'Τέλος μηνύματος': {}", body);
        } else {
            panic!("Expected cleaned_plain_text_body to contain Greek text");
        }
        Ok(())
    }

    #[test]
    fn test_parse_mbox_greek_characters() -> Result<(), Box<dyn std::error::Error>> {
        // Create an mbox file with Greek characters
        let mbox_content = r#"From <EMAIL> Mon Jan  1 10:00:00 2024
From: <EMAIL>
Subject: =?UTF-8?B?zp/Ouc66zr/Ovc6/zrzOuc66z4wgzrXOuc+CzrHOuc+EzrfPgc65zrE=?=
To: <EMAIL>
Content-Type: text/plain; charset="utf-8"

Γεια σας! Αυτό είναι ένα email με ελληνικούς χαρακτήρες στο mbox format.

"#;

        let mut file = NamedTempFile::new()?;
        file.write_all(mbox_content.as_bytes())?;
        let file_path = file.path().to_str().unwrap();

        let parsed_emails = parse_mbox(file_path)?;
        assert_eq!(parsed_emails.len(), 1);

        let email = &parsed_emails[0];
        assert!(email.subject.is_some());

        // Verify that Greek characters are preserved in the body
        if let Some(body) = &email.cleaned_plain_text_body {
            assert!(body.contains("Γεια σας"), "Body should contain 'Γεια σας': {}", body);
            assert!(body.contains("ελληνικούς χαρακτήρες"), "Body should contain 'ελληνικούς χαρακτήρες': {}", body);
        } else {
            panic!("Expected cleaned_plain_text_body to contain Greek text");
        }
        Ok(())
    }

    #[test]
    fn test_parse_invalid_file_path() {
        let result = parse_eml("non_existent_file.eml");
        assert!(result.is_err());
    }

    #[test]
    fn test_parse_empty_file() -> Result<(), Box<dyn std::error::Error>> {
        let mut file = NamedTempFile::new()?;
        // Write empty content
        file.write_all(b"")?;
        let file_path = file.path().to_str().unwrap();

        let result = parse_eml(file_path);
        // Should handle empty files gracefully
        assert!(result.is_ok() || result.is_err()); // Either outcome is acceptable
        Ok(())
    }
}
